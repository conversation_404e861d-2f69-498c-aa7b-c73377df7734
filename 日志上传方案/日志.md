2025-08-28 16:34:57.324 11783-11783 Glide                   com.example.repairorderapp           W  Load failed for [https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/3f0a2650-83e3-11f0-ac2c-c9949e082118] with dimensions [263x263]
class com.bumptech.glide.load.engine.GlideException: Failed to load resource
Cause (1 of 1): class com.bumptech.glide.load.engine.GlideException: Failed LoadPath{RecyclableBufferedInputStream->Object->Drawable}, REMOTE, https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/3f0a2650-83e3-11f0-ac2c-c9949e082118
Cause (1 of 4): class com.bumptech.glide.load.engine.GlideException: Failed DecodePath{RecyclableBufferedInputStream->Drawable->Drawable}
Cause (2 of 4): class com.bumptech.glide.load.engine.GlideException: Failed DecodePath{RecyclableBufferedInputStream->GifDrawable->Drawable}
Cause (3 of 4): class com.bumptech.glide.load.engine.GlideException: Failed DecodePath{RecyclableBufferedInputStream->Bitmap->BitmapDrawable}
Cause (4 of 4): class com.bumptech.glide.load.engine.GlideException: Failed DecodePath{RecyclableBufferedInputStream->BitmapDrawable->Drawable}
2025-08-28 16:34:57.325 11783-11783 FaultImageAdapter       com.example.repairorderapp           E  图片加载失败: https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/3f0a2650-83e3-11f0-ac2c-c9949e082118
class com.bumptech.glide.load.engine.GlideException: Failed to load resource
Cause (1 of 1): class com.bumptech.glide.load.engine.GlideException: Failed LoadPath{RecyclableBufferedInputStream->Object->Drawable}, REMOTE, https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/3f0a2650-83e3-11f0-ac2c-c9949e082118
Cause (1 of 4): class com.bumptech.glide.load.engine.GlideException: Failed DecodePath{RecyclableBufferedInputStream->Drawable->Drawable}
Cause (2 of 4): class com.bumptech.glide.load.engine.GlideException: Failed DecodePath{RecyclableBufferedInputStream->GifDrawable->Drawable}
Cause (3 of 4): class com.bumptech.glide.load.engine.GlideException: Failed DecodePath{RecyclableBufferedInputStream->Bitmap->BitmapDrawable}
Cause (4 of 4): class com.bumptech.glide.load.engine.GlideException: Failed DecodePath{RecyclableBufferedInputStream->BitmapDrawable->Drawable}

2025-08-28 16:38:21.055 11783-11915 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=522.76ms min=5.22ms max=3584.19ms count=7
2025-08-28 16:38:21.068 11783-11783 RepairOrders            com.example.repairorderapp           D  保存列表位置: position=1, offset=-135
2025-08-28 16:38:21.123 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  开始加载工单详情, ID: 1960946770762645506
2025-08-28 16:38:21.123 11783-11783 GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: WorkOrderApi_getWorkOrderDetail
2025-08-28 16:38:21.123 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer/work-order/detail/1960946770762645506
2025-08-28 16:38:21.125 11783-11783 AutofillManager         com.example.repairorderapp           D  view not autofillable - not passing ime action check
2025-08-28 16:38:21.125 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-08-28 16:38:21.126 11783-11783 AutofillManager         com.example.repairorderapp           D  view not autofillable - not passing ime action check
2025-08-28 16:38:21.126 11783-11974 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer/work-order/detail/1960946770762645506
2025-08-28 16:38:21.126 11783-11974 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer/work-order/detail/1960946770762645506
2025-08-28 16:38:21.126 11783-11974 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-28 16:38:21.126 11783-11974 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-28 16:38:21.126 11783-11974 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-28 16:38:21.126 11783-11974 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: eba17b34-9...
2025-08-28 16:38:21.126 11783-11974 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-28 16:38:21.126 11783-11974 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-28 16:38:21.126 11783-11974 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-28 16:38:21.126 11783-11974 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-28 16:38:21.126 11783-11974 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-28 16:38:21.126 11783-11974 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-28 16:38:21.126 11783-11974 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-28 16:38:21.140 11783-11783 RecyclerView            com.example.repairorderapp           E  No adapter attached; skipping layout
2025-08-28 16:38:21.211 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer/work-order/detail/1960946770762645506 (85ms)
2025-08-28 16:38:21.211 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-28 16:38:21.211 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Thu, 28 Aug 2025 08:38:23 GMT
2025-08-28 16:38:21.211 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-08-28 16:38:21.211 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-08-28 16:38:21.211 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-08-28 16:38:21.212 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"id":"1960946770762645506","code":"WXGD250828000004","productId":"1730431526003167469","machineNum":"MC2501150147","deviceGroupId":"1881694874777022465","customerId":"1003410000","customerStaffId":"1943231342783262721","status":{"value":"engineer_arrive","label":"工程师到达"},"isAppeal":false,"currentProcess":"ENGINEER_ARRIVE","errorCode":"0","excDesc":"起杠子","excPics":[{"key":"prod/f15880e0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f15880e0-83d4-11f0-a497-2d258384740f"},{"key":"prod/f197acc0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f197acc0-83d4-11f0-a497-2d258384740f"},{"key":"prod/f1b546e0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f1b546e0-83d4-11f0-a497-2d258384740f"},{"key":"prod/f1ce4d20-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f1ce4d20-83d4-11f0-a497-2d258384740f"},{"key":"prod/f1fb77a0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f1fb77a0-83d4-11f0-a497-2d258384740f"},{"key":"prod/f25afcc0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f25afcc0-83d4-11f0-a497-2d258384740f"}],"expectArriveTime":"2025-08-29 08:35:20","orderReceiveTime":"2025-08-28 15:09:26","prospectArriveTime":"2025-08-29 08:35:20","departureTime":"2025-08-28 15:34:43","actualArriveTime":"2025-08-28 15:56:39","repairPay":"250.00","replacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"0.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"300.00","totalPay":"300.00","isAssignEngineer":false,"engineerId":{"id":"1901815444382265345","code":"SCZJZY0027","name":"舒茂林","isAvailable":true},"isEvaluated":false,"fiveColourCount":0,"printCount":0,"createdAt":"2025-08-28 14:05:17","updatedAt":"2025-08-28 15:56:39","deleted":0,"serType":{"label":"购机全保","value":"BUY_FULL"},"isContracted":true,"isAllServe":true,"customer":{"id":"1003410000","seqId":"KH250109000161","name":"宾得包装-金泉路","source":{"value":"2301","label":"后台录入"},"type":{"value":"401","label":"单店"},"status":{"value":"102","label":"注册账号"},"membershipLevel":{"value":"normal","label":"注册"},"shopRecruitment":"宾得包装","subbranch":"金泉路","businessStatus":{"value":"201","label":"营业中"},"industryAttr":{"value":"310","label":"企业"},"legalPerson":"徐标初","legalPersonTel":"***********","longitude":"104.013437","latitude":"30.715147","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.715147","longitude":"104.013437"},"regionCode":510106,"isContracted":false,"isAll":false,"pointsBalance":0,"address":"金泉路5号蓝灵集团 ","createdAt":"2025-01-10 11:29:33","updatedAt":"2025-03-19 15:49:27","updatedBy":"1871487313813491714","deleted":0},"customerRegion":"四川省成都市金牛区","brand":"理光","machine":"Pro C7210X","customerStaff":{"id":"1943231342783262721","customerId":"1003410000","name":"周霖","tel":"***********","role":{"value":"504","label":"报修员"},"status":true,"deleted":0,"createdAt":"2025-07-10 16:50:30","updatedAt":"2025-07-10 16:50:30","isCollect":false,"vxGroupName":{"value":"2680","label":"未入群"}},"customerDeviceGroup":{"id":"1881694874777022465","customerId":"1003410000","deviceSeqId":"D25012100073","deviceGroup":{"value":"701","label":"1号机"},"productId":"1730431526003167469","deviceStatus":{"value":"903","label":"维修中"},"status":true,"deviceOn":{"value":"1102","label":"再生机"},"treatyType":{"value":"1201"},"fixStatus":{"value":"1501","label":"正常"},"machineNum":"MC2501150147","paperType":"A4","macNumber":"A0-42-3F-3A-8A-E4","enableStatistics":true,"blackWhiteCounter":1602262,"colorCounter":888228,"fiveColourCounter":0,"adjustBlackWhite":0,"adjustColor":0,"deleted":0
2025-08-28 16:38:21.212 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  rType":{"label":"购机全保","value":"BUY_FULL"}},"travelTime":"21分钟","fixTime":""}}
2025-08-28 16:38:21.212 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (4278-byte body)
2025-08-28 16:38:21.253 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  工单详情API响应: 200
2025-08-28 16:38:21.253 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  开始解析工单数据，类型: com.google.gson.internal.LinkedTreeMap
2025-08-28 16:38:21.254 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  Map数据解析，可用字段: id, code, productId, machineNum, deviceGroupId, customerId, customerStaffId, status, isAppeal, currentProcess, errorCode, excDesc, excPics, expectArriveTime, orderReceiveTime, prospectArriveTime, departureTime, actualArriveTime, repairPay, replacePay, visitPay, longWayVisitPay, discountAmount, derateAmount, additionalPay, engineerAdditionalPay, itemPay, totalAmount, totalPay, isAssignEngineer, engineerId, isEvaluated, fiveColourCount, printCount, createdAt, updatedAt, deleted, serType, isContracted, isAllServe, customer, customerRegion, brand, machine, customerStaff, customerDeviceGroup, travelTime, fixTime
2025-08-28 16:38:21.254 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  工单ID: 1960946770762645506
2025-08-28 16:38:21.254 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  客户信息详细数据 - customerId: 1003410000, customerName: 宾得包装-金泉路
2025-08-28 16:38:21.255 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  customer对象: {id=1003410000, seqId=KH250109000161, name=宾得包装-金泉路, source={value=2301, label=后台录入}, type={value=401, label=单店}, status={value=102, label=注册账号}, membershipLevel={value=normal, label=注册}, shopRecruitment=宾得包装, subbranch=金泉路, businessStatus={value=201, label=营业中}, industryAttr={value=310, label=企业}, legalPerson=徐标初, legalPersonTel=***********, longitude=104.013437, latitude=30.715147, location={system={label=火星/国测局坐标系, value=GCJ_02}, latitude=30.715147, longitude=104.013437}, regionCode=510106.0, isContracted=false, isAll=false, pointsBalance=0.0, address=金泉路5号蓝灵集团 , createdAt=2025-01-10 11:29:33, updatedAt=2025-03-19 15:49:27, updatedBy=1871487313813491714, deleted=0.0}
2025-08-28 16:38:21.255 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  所有报修用户相关字段: {customerStaff={id=1943231342783262721, customerId=1003410000, name=周霖, tel=***********, role={value=504, label=报修员}, status=true, deleted=0.0, createdAt=2025-07-10 16:50:30, updatedAt=2025-07-10 16:50:30, isCollect=false, vxGroupName={value=2680, label=未入群}}}
2025-08-28 16:38:21.255 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  所有地址相关字段: {customerRegion=四川省成都市金牛区}
2025-08-28 16:38:21.255 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  从customerRegion+detailAddress组合获取地址: 四川省成都市金牛区
2025-08-28 16:38:21.256 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  最终解析到的地址: 四川省成都市金牛区
2025-08-28 16:38:21.256 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  从顶级字段获取经纬度: lat=0.0, lng=0.0
2025-08-28 16:38:21.256 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  从customer.location获取经纬度: lat=30.715147, lng=104.013437
2025-08-28 16:38:21.257 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  检测到故障照片: 6张
2025-08-28 16:38:21.257 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  照片 0: https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f15880e0-83d4-11f0-a497-2d258384740f
2025-08-28 16:38:21.257 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  照片 1: https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f197acc0-83d4-11f0-a497-2d258384740f
2025-08-28 16:38:21.257 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  照片 2: https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f1b546e0-83d4-11f0-a497-2d258384740f
2025-08-28 16:38:21.257 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  照片 3: https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f1ce4d20-83d4-11f0-a497-2d258384740f
2025-08-28 16:38:21.257 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  照片 4: https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f1fb77a0-83d4-11f0-a497-2d258384740f
2025-08-28 16:38:21.257 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  照片 5: https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f25afcc0-83d4-11f0-a497-2d258384740f
2025-08-28 16:38:21.258 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  解析Customer对象 - id: 1003410000, name: 宾得包装-金泉路, address: 金泉路5号蓝灵集团
2025-08-28 16:38:21.258 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  解析设备组信息：原始数据=null, customerDeviceGroup={id=1881694874777022465, customerId=1003410000, deviceSeqId=D25012100073, deviceGroup={value=701, label=1号机}, productId=1730431526003167469, deviceStatus={value=903, label=维修中}, status=true, deviceOn={value=1102, label=再生机}, treatyType={value=1201}, fixStatus={value=1501, label=正常}, machineNum=MC2501150147, paperType=A4, macNumber=A0-42-3F-3A-8A-E4, enableStatistics=true, blackWhiteCounter=1602262.0, colorCounter=888228.0, fiveColourCounter=0.0, adjustBlackWhite=0.0, adjustColor=0.0, deleted=0.0, createdAt=2025-01-21 21:26:33, updatedAt=2025-04-08 14:03:47, regCliState=1, installAt=2025-04-08 14:03:47, dataShowState=1.0, serType={label=购机全保, value=BUY_FULL}}, deviceGroupId=1881694874777022465, deviceGroupName=null, 解析结果label=1号机, value=701
2025-08-28 16:38:21.258 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  数据解析成功: ID=1960946770762645506, 客户=宾得包装-金泉路, 机型=null, 工程师=舒茂林, 设备组=1号机
2025-08-28 16:38:21.258 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  从customerStaff获取报修人信息: 姓名=周霖, 电话=***********
2025-08-28 16:38:21.258 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  最终报修人信息: 姓名=周霖, 电话=***********
2025-08-28 16:38:21.259 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  映射到RepairOrder - 设备组原始信息: 1号机, 值: 701
2025-08-28 16:38:21.259 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  故障照片映射结果: 6张
2025-08-28 16:38:21.263 11783-12124 WorkOrderDetail         com.example.repairorderapp           D  已存储产品ID: 1730431526003167469
2025-08-28 16:38:21.270 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  设置设备信息 - deviceGroup: '1号机', machineModel: '理光/Pro C7210X', serviceType: '购机全保'
2025-08-28 16:38:21.270 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  工单状态 - status: '工程师到达', statusValue: 'engineer_arrive'
2025-08-28 16:38:21.271 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  显示地址信息 - 原始值: '金泉路5号蓝灵集团 ', 显示值: '金泉路5号蓝灵集团 '
2025-08-28 16:38:21.272 11783-11783 GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: WorkOrderApi_getEngineer
2025-08-28 16:38:21.274 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  工程师信息 - 工程师ID: '1901815444382265345', 工程师姓名: '舒茂林'
2025-08-28 16:38:21.274 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/detail/1901815444382265345
2025-08-28 16:38:21.274 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-08-28 16:38:21.274 11783-11974 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/detail/1901815444382265345
2025-08-28 16:38:21.275 11783-11974 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/detail/1901815444382265345
2025-08-28 16:38:21.275 11783-11974 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-28 16:38:21.275 11783-11974 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-28 16:38:21.276 11783-11974 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-28 16:38:21.276 11783-11783 GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: WorkOrderApi_getWorkDetail
2025-08-28 16:38:21.277 11783-11974 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: eba17b34-9...
2025-08-28 16:38:21.277 11783-11974 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-28 16:38:21.277 11783-11974 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-28 16:38:21.277 11783-11783 RepairOrderDetail       com.example.repairorderapp           D  显示故障照片：6张
2025-08-28 16:38:21.277 11783-11974 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-28 16:38:21.278 11783-12106 okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/work-order/detail/1960946770762645506
2025-08-28 16:38:21.279 11783-12106 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-08-28 16:38:21.279 11783-12106 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/work-order/detail/1960946770762645506
2025-08-28 16:38:21.279 11783-11974 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-28 16:38:21.279 11783-11974 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-28 16:38:21.280 11783-12106 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/work-order/detail/1960946770762645506
2025-08-28 16:38:21.281 11783-12106 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-28 16:38:21.281 11783-12106 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-28 16:38:21.282 11783-12106 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-28 16:38:21.282 11783-12106 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: eba17b34-9...
2025-08-28 16:38:21.282 11783-12106 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-28 16:38:21.283 11783-12106 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-28 16:38:21.283 11783-12106 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-28 16:38:21.283 11783-12106 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-28 16:38:21.284 11783-11974 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-28 16:38:21.284 11783-11974 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-28 16:38:21.284 11783-12106 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-28 16:38:21.284 11783-12106 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-28 16:38:21.284 11783-12106 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-28 16:38:21.286 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  显示报修人信息 - 报修人: '周霖', 电话: '***********'
2025-08-28 16:38:21.287 11783-11783 GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: WorkOrderApi_getWorkOrderEvaluationDetail
2025-08-28 16:38:21.289 11783-12105 okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/work-order-pc/pcDetail/1960946770762645506
2025-08-28 16:38:21.293 11783-12105 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-08-28 16:38:21.294 11783-12105 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/work-order-pc/pcDetail/1960946770762645506
2025-08-28 16:38:21.295 11783-12105 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/work-order-pc/pcDetail/1960946770762645506
2025-08-28 16:38:21.295 11783-12105 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-28 16:38:21.298 11783-12105 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-28 16:38:21.299 11783-12105 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-28 16:38:21.300 11783-12105 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: eba17b34-9...
2025-08-28 16:38:21.300 11783-12105 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-28 16:38:21.300 11783-12105 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-28 16:38:21.300 11783-12105 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-28 16:38:21.300 11783-12105 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-28 16:38:21.300 11783-12105 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-28 16:38:21.300 11783-12105 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-28 16:38:21.300 11783-12105 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-28 16:38:21.361 11783-12106 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/work-order/detail/1960946770762645506 (81ms)
2025-08-28 16:38:21.361 11783-12106 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-28 16:38:21.362 11783-12106 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Thu, 28 Aug 2025 08:38:24 GMT
2025-08-28 16:38:21.362 11783-12106 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-08-28 16:38:21.362 11783-12106 okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-08-28 16:38:21.362 11783-12106 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-08-28 16:38:21.362 11783-12106 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"id":"1960946770762645506","code":"WXGD250828000004","productId":"1730431526003167469","machineNum":"MC2501150147","deviceGroupId":"1881694874777022465","customerId":"1003410000","customerStaffId":"1943231342783262721","status":{"value":"engineer_arrive","label":"工程师到达"},"isAppeal":false,"currentProcess":"ENGINEER_ARRIVE","errorCode":"0","excDesc":"起杠子","excPics":[{"key":"prod/f15880e0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f15880e0-83d4-11f0-a497-2d258384740f"},{"key":"prod/f197acc0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f197acc0-83d4-11f0-a497-2d258384740f"},{"key":"prod/f1b546e0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f1b546e0-83d4-11f0-a497-2d258384740f"},{"key":"prod/f1ce4d20-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f1ce4d20-83d4-11f0-a497-2d258384740f"},{"key":"prod/f1fb77a0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f1fb77a0-83d4-11f0-a497-2d258384740f"},{"key":"prod/f25afcc0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f25afcc0-83d4-11f0-a497-2d258384740f"}],"expectArriveTime":"2025-08-29 08:35:20","orderReceiveTime":"2025-08-28 15:09:26","prospectArriveTime":"2025-08-29 08:35:20","departureTime":"2025-08-28 15:34:43","actualArriveTime":"2025-08-28 15:56:39","repairPay":"250.00","replacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"0.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"300.00","totalPay":"300.00","isAssignEngineer":false,"engineerId":{"id":"1901815444382265345","code":"SCZJZY0027","name":"舒茂林","isAvailable":true},"isEvaluated":false,"fiveColourCount":0,"printCount":0,"createdAt":"2025-08-28 14:05:17","updatedAt":"2025-08-28 15:56:39","deleted":0,"serType":{"label":"购机全保","value":"BUY_FULL"},"isContracted":true,"isAllServe":true,"cdg":{"id":"1881694874777022465","customerId":"1003410000","deviceSeqId":"D25012100073","deviceGroup":{"value":"701","label":"1号机"},"productId":"1730431526003167469","deviceStatus":{"value":"903","label":"维修中"},"status":true,"deviceOn":{"value":"1102","label":"再生机"},"treatyType":{"value":"1201"},"fixStatus":{"value":"1501","label":"正常"},"machineNum":"MC2501150147","paperType":"A4","macNumber":"A0-42-3F-3A-8A-E4","enableStatistics":true,"blackWhiteCounter":1602262,"colorCounter":888228,"fiveColourCounter":0,"adjustBlackWhite":0,"adjustColor":0,"deleted":0,"createdAt":"2025-01-21 21:26:33","updatedAt":"2025-04-08 14:03:47","productInfo":"理光/Pro C7210X","regCliState":"1","installAt":"2025-04-08 14:03:47","dataShowState":1,"serType":{"label":"购机全保","value":"BUY_FULL"}},"travelTime":"21分钟","fixTime":"","engineerMobile":"17321858976","currBlackCount":0,"currColorCount":0}}
2025-08-28 16:38:21.362 11783-12106 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (3072-byte body)
2025-08-28 16:38:21.370 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  开始解析工单数据，类型: com.google.gson.internal.LinkedTreeMap
2025-08-28 16:38:21.370 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  Map数据解析，可用字段: id, code, productId, machineNum, deviceGroupId, customerId, customerStaffId, status, isAppeal, currentProcess, errorCode, excDesc, excPics, expectArriveTime, orderReceiveTime, prospectArriveTime, departureTime, actualArriveTime, repairPay, replacePay, visitPay, longWayVisitPay, discountAmount, derateAmount, additionalPay, engineerAdditionalPay, itemPay, totalAmount, totalPay, isAssignEngineer, engineerId, isEvaluated, fiveColourCount, printCount, createdAt, updatedAt, deleted, serType, isContracted, isAllServe, cdg, travelTime, fixTime, engineerMobile, currBlackCount, currColorCount
2025-08-28 16:38:21.370 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  工单ID: 1960946770762645506
2025-08-28 16:38:21.370 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  客户信息详细数据 - customerId: 1003410000, customerName:
2025-08-28 16:38:21.371 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  customer对象: null
2025-08-28 16:38:21.371 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  所有报修用户相关字段: {}
2025-08-28 16:38:21.371 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  所有地址相关字段: {}
2025-08-28 16:38:21.371 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  从备选字段获取地址:
2025-08-28 16:38:21.371 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  最终解析到的地址:
2025-08-28 16:38:21.371 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  从顶级字段获取经纬度: lat=0.0, lng=0.0
2025-08-28 16:38:21.371 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  检测到故障照片: 6张
2025-08-28 16:38:21.371 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  照片 0: https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f15880e0-83d4-11f0-a497-2d258384740f
2025-08-28 16:38:21.371 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  照片 1: https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f197acc0-83d4-11f0-a497-2d258384740f
2025-08-28 16:38:21.371 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  照片 2: https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f1b546e0-83d4-11f0-a497-2d258384740f
2025-08-28 16:38:21.371 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  照片 3: https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f1ce4d20-83d4-11f0-a497-2d258384740f
2025-08-28 16:38:21.371 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  照片 4: https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f1fb77a0-83d4-11f0-a497-2d258384740f
2025-08-28 16:38:21.371 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  照片 5: https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f25afcc0-83d4-11f0-a497-2d258384740f
2025-08-28 16:38:21.372 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  Customer对象为null，使用备用地址:
2025-08-28 16:38:21.373 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  解析设备组信息：原始数据=null, customerDeviceGroup=null, deviceGroupId=1881694874777022465, deviceGroupName=null, 解析结果label=, value=1881694874777022465
2025-08-28 16:38:21.373 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  数据解析成功: ID=1960946770762645506, 客户=, 机型=null, 工程师=舒茂林, 设备组=
2025-08-28 16:38:21.373 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  最终报修人信息: 姓名=, 电话=
2025-08-28 16:38:21.373 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  映射到RepairOrder - 设备组原始信息: , 值: 1881694874777022465
2025-08-28 16:38:21.373 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  故障照片映射结果: 6张
2025-08-28 16:38:21.375 11783-12105 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/work-order-pc/pcDetail/1960946770762645506 (80ms)
2025-08-28 16:38:21.375 11783-12105 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-28 16:38:21.375 11783-12105 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Thu, 28 Aug 2025 08:38:24 GMT
2025-08-28 16:38:21.375 11783-12105 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-08-28 16:38:21.376 11783-12105 okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-08-28 16:38:21.376 11783-12105 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-08-28 16:38:21.376 11783-12105 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"id":"1960946770762645506","code":"WXGD250828000004","productId":"1730431526003167469","machineNum":"MC2501150147","deviceGroupId":"1881694874777022465","customerId":"1003410000","customerStaffId":"1943231342783262721","status":{"value":"engineer_arrive","label":"工程师到达"},"isAppeal":false,"currentProcess":"ENGINEER_ARRIVE","errorCode":"0","excDesc":"起杠子","excPics":[{"key":"prod/f15880e0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f15880e0-83d4-11f0-a497-2d258384740f"},{"key":"prod/f197acc0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f197acc0-83d4-11f0-a497-2d258384740f"},{"key":"prod/f1b546e0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f1b546e0-83d4-11f0-a497-2d258384740f"},{"key":"prod/f1ce4d20-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f1ce4d20-83d4-11f0-a497-2d258384740f"},{"key":"prod/f1fb77a0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f1fb77a0-83d4-11f0-a497-2d258384740f"},{"key":"prod/f25afcc0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f25afcc0-83d4-11f0-a497-2d258384740f"}],"expectArriveTime":"2025-08-29 08:35:20","orderReceiveTime":"2025-08-28 15:09:26","prospectArriveTime":"2025-08-29 08:35:20","departureTime":"2025-08-28 15:34:43","actualArriveTime":"2025-08-28 15:56:39","repairPay":"250.00","replacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"0.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"300.00","totalPay":"300.00","isAssignEngineer":false,"engineerId":{"id":"1901815444382265345","code":"SCZJZY0027","name":"舒茂林","isAvailable":true},"isEvaluated":false,"fiveColourCount":0,"printCount":0,"createdAt":"2025-08-28 14:05:17","updatedAt":"2025-08-28 15:56:39","deleted":0,"serType":{"label":"购机全保","value":"BUY_FULL"},"deviceGroup":{"value":"701","label":"1号机"},"productInfo":"理光/Pro C7210X","customer":{"id":"1003410000","seqId":"KH250109000161","name":"宾得包装-金泉路","source":{"value":"2301","label":"后台录入"},"type":{"value":"401","label":"单店"},"status":{"value":"102","label":"注册账号"},"membershipLevel":{"value":"normal","label":"注册"},"shopRecruitment":"宾得包装","subbranch":"金泉路","businessStatus":{"value":"201","label":"营业中"},"industryAttr":{"value":"310","label":"企业"},"legalPerson":"徐标初","legalPersonTel":"***********","longitude":"104.013437","latitude":"30.715147","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.715147","longitude":"104.013437"},"regionCode":510106,"isContracted":false,"isAll":false,"pointsBalance":0,"address":"金泉路5号蓝灵集团 ","createdAt":"2025-01-10 11:29:33","updatedAt":"2025-03-19 15:49:27","updatedBy":"1871487313813491714","deleted":0},"appealCount":"0","travelTime":"21分钟","laborCost":"300.00","fixTime":"","contactPhone":"***********","customerStaff":"周霖","departureAddress":"四川省成都市金牛区马河湾巷"}}
2025-08-28 16:38:21.376 11783-12105 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (3285-byte body)
2025-08-28 16:38:21.562 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/detail/1901815444382265345 (287ms)
2025-08-28 16:38:21.562 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-28 16:38:21.562 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Thu, 28 Aug 2025 08:38:24 GMT
2025-08-28 16:38:21.562 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-08-28 16:38:21.562 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-08-28 16:38:21.562 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-08-28 16:38:21.564 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"engineerInfo":{"id":"1901815444382265345","introduce":"维修学徒","yearOfService":0,"regionCode":[510104,510105,510106,510107,510108,510112,510113,510114,510115,510116,510117,510118,510121,510129,510131,510181,510182,510183,510184,510185,510302,510303,510304,510311,510321,510322,510402,510403,510411,510421,510422,510502,510503,510504,510521,510522,510524,510525,510603,510604,510623,510681,510682,510683,510703,510704,510705,510722,510723,510725,510726,510727,510781,510802,510811,510812,510821,510822,510823,510824,510903,510904,510921,510923,510981,511002,511011,511024,511025,511083,511102,511111,511112,511113,511123,511124,511126,511129,511132,511133,511181,511302,511303,511304,511321,511322,511323,511324,511325,511381,511402,511403,511421,511423,511424,511425,511502,511503,511504,511523,511524,511525,511526,511527,511528,511529,511602,511603,511621,511622,511623,511681,511702,511703,511722,511723,511724,511725,511781,511802,511803,511822,511823,511824,511825,511826,511827,511902,511903,511921,511922,511923,512002,512021,512022,513201,513221,513222,513223,513224,513225,513226,513227,513228,513230,513231,513232,513233,513301,513322,513323,513324,513325,513326,513327,513328,513329,513330,513331,513332,513333,513334,513335,513336,513337,513338,513401,513402,513422,513423,513424,513426,513427,513428,513429,513430,513431,513432,513433,513434,513435,513436,513437],"locationRegionCode":510108},"userManageVo":{"id":"1901815444382265345","code":"SCZJZY0027","name":"舒茂林","mobileNumber":"17321858976","identityCardNumber":"511028199906092916","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},"engineerSkills":[{"productId":"1730431526003167490","brand":"理光","serial":"IM C6000系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526003167496","brand":"理光","serial":"MP C6004系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526003167503","brand":"理光","serial":"MP C3504系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526003167515","brand":"理光","serial":"MP C2503系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526003167520","brand":"理光","serial":"MP C2504系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526003167525","brand":"理光","serial":"MP C6003系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526003167529","brand":"理光","serial":"MP C3503系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526011556127","brand":"理光","serial":"Pro C5200系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526003167415","brand":"理光","serial":"MP 7500系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526003167419","brand":"理光","serial":"MP 7503系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526003167426","brand":"理光","serial":"MP 7001系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526003167431","brand":"理光","serial":"MP 7000系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526003167435","brand":"理光","serial":"IM 8000系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526003167439","brand":"理光","serial":"MP 7502系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526011556057","brand":"理光","serial":"MP 6055系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526011556064","brand":"理光","serial":"MP 6054系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526011556071","brand":"理光","serial":"MP 3554系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526003167392","brand":"理光","serial":"Pro 8100系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526003167382","brand":"理光"
2025-08-28 16:38:21.565 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  11556110","brand":"理光","serial":"Pro C5100系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526011556119","brand":"理光","serial":"Pro C5300系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1877611551758757889","brand":"理光","serial":"Pro C7500系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526003167693","brand":"理光","serial":"MP W5100系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526003167597","brand":"理光","serial":"Pro C9100系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526003167600","brand":"理光","serial":"Pro C9200系列","skillExp":{"value":"5001","label":"普通"}},{"productId":"1730431526003167461","brand":"理光","serial":"Pro C7200系列","skillExp":{"value":"5001","label":"普通"}}],"orderNumber":"117","region":[{"code":510104,"parentCode":510100,"name":"锦江区","path":"/510000/510100/510104","hasChildren":false,"fullRegion":"四川省成都市锦江区"},{"code":510105,"parentCode":510100,"name":"青羊区","path":"/510000/510100/510105","hasChildren":false,"fullRegion":"四川省成都市青羊区"},{"code":510106,"parentCode":510100,"name":"金牛区","path":"/510000/510100/510106","hasChildren":false,"fullRegion":"四川省成都市金牛区"},{"code":510107,"parentCode":510100,"name":"武侯区","path":"/510000/510100/510107","hasChildren":false,"fullRegion":"四川省成都市武侯区"},{"code":510108,"parentCode":510100,"name":"成华区","path":"/510000/510100/510108","hasChildren":false,"fullRegion":"四川省成都市成华区"},{"code":510112,"parentCode":510100,"name":"龙泉驿区","path":"/510000/510100/510112","hasChildren":false,"fullRegion":"四川省成都市龙泉驿区"},{"code":510113,"parentCode":510100,"name":"青白江区","path":"/510000/510100/510113","hasChildren":false,"fullRegion":"四川省成都市青白江区"},{"code":510114,"parentCode":510100,"name":"新都区","path":"/510000/510100/510114","hasChildren":false,"fullRegion":"四川省成都市新都区"},{"code":510115,"parentCode":510100,"name":"温江区","path":"/510000/510100/510115","hasChildren":false,"fullRegion":"四川省成都市温江区"},{"code":510116,"parentCode":510100,"name":"双流区","path":"/510000/510100/510116","hasChildren":false,"fullRegion":"四川省成都市双流区"},{"code":510117,"parentCode":510100,"name":"郫都区","path":"/510000/510100/510117","hasChildren":false,"fullRegion":"四川省成都市郫都区"},{"code":510118,"parentCode":510100,"name":"新津区","path":"/510000/510100/510118","hasChildren":false,"fullRegion":"四川省成都市新津区"},{"code":510121,"parentCode":510100,"name":"金堂县","path":"/510000/510100/510121","hasChildren":false,"fullRegion":"四川省成都市金堂县"},{"code":510129,"parentCode":510100,"name":"大邑县","path":"/510000/510100/510129","hasChildren":false,"fullRegion":"四川省成都市大邑县"},{"code":510131,"parentCode":510100,"name":"蒲江县","path":"/510000/510100/510131","hasChildren":false,"fullRegion":"四川省成都市蒲江县"},{"code":510181,"parentCode":510100,"name":"都江堰市","path":"/510000/510100/510181","hasChildren":false,"fullRegion":"四川省成都市都江堰市"},{"code":510182,"parentCode":510100,"name":"彭州市","path":"/510000/510100/510182","hasChildren":false,"fullRegion":"四川省成都市彭州市"},{"code":510183,"parentCode":510100,"name":"邛崃市","path":"/510000/510100/510183","hasChildren":false,"fullRegion":"四川省成都市邛崃市"},{"code":510184,"parentCode":510100,"name":"崇州市","path":"/510000/510100/510184","hasChildren":false,"fullRegion":"四川省成都市崇州市"},{"code":510185,"parentCode":510100,"name":"简阳市","path":"/510000/510100/510185","hasChildren":false,"fullRegion":"四川省成都市简阳市"},{"code":510302,"parentCode":510300,"name":"自流井区","path":"/510000/510300/510302","hasChildren":false,"fullRegion":"四川省自贡市自流井区"},{"code":51030
2025-08-28 16:38:21.565 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  0322","hasChildren":false,"fullRegion":"四川省自贡市富顺县"},{"code":510402,"parentCode":510400,"name":"东区","path":"/510000/510400/510402","hasChildren":false,"fullRegion":"四川省攀枝花市东区"},{"code":510403,"parentCode":510400,"name":"西区","path":"/510000/510400/510403","hasChildren":false,"fullRegion":"四川省攀枝花市西区"},{"code":510411,"parentCode":510400,"name":"仁和区","path":"/510000/510400/510411","hasChildren":false,"fullRegion":"四川省攀枝花市仁和区"},{"code":510421,"parentCode":510400,"name":"米易县","path":"/510000/510400/510421","hasChildren":false,"fullRegion":"四川省攀枝花市米易县"},{"code":510422,"parentCode":510400,"name":"盐边县","path":"/510000/510400/510422","hasChildren":false,"fullRegion":"四川省攀枝花市盐边县"},{"code":510502,"parentCode":510500,"name":"江阳区","path":"/510000/510500/510502","hasChildren":false,"fullRegion":"四川省泸州市江阳区"},{"code":510503,"parentCode":510500,"name":"纳溪区","path":"/510000/510500/510503","hasChildren":false,"fullRegion":"四川省泸州市纳溪区"},{"code":510504,"parentCode":510500,"name":"龙马潭区","path":"/510000/510500/510504","hasChildren":false,"fullRegion":"四川省泸州市龙马潭区"},{"code":510521,"parentCode":510500,"name":"泸县","path":"/510000/510500/510521","hasChildren":false,"fullRegion":"四川省泸州市泸县"},{"code":510522,"parentCode":510500,"name":"合江县","path":"/510000/510500/510522","hasChildren":false,"fullRegion":"四川省泸州市合江县"},{"code":510524,"parentCode":510500,"name":"叙永县","path":"/510000/510500/510524","hasChildren":false,"fullRegion":"四川省泸州市叙永县"},{"code":510525,"parentCode":510500,"name":"古蔺县","path":"/510000/510500/510525","hasChildren":false,"fullRegion":"四川省泸州市古蔺县"},{"code":510603,"parentCode":510600,"name":"旌阳区","path":"/510000/510600/510603","hasChildren":false,"fullRegion":"四川省德阳市旌阳区"},{"code":510604,"parentCode":510600,"name":"罗江区","path":"/510000/510600/510604","hasChildren":false,"fullRegion":"四川省德阳市罗江区"},{"code":510623,"parentCode":510600,"name":"中江县","path":"/510000/510600/510623","hasChildren":false,"fullRegion":"四川省德阳市中江县"},{"code":510681,"parentCode":510600,"name":"广汉市","path":"/510000/510600/510681","hasChildren":false,"fullRegion":"四川省德阳市广汉市"},{"code":510682,"parentCode":510600,"name":"什邡市","path":"/510000/510600/510682","hasChildren":false,"fullRegion":"四川省德阳市什邡市"},{"code":510683,"parentCode":510600,"name":"绵竹市","path":"/510000/510600/510683","hasChildren":false,"fullRegion":"四川省德阳市绵竹市"},{"code":510703,"parentCode":510700,"name":"涪城区","path":"/510000/510700/510703","hasChildren":false,"fullRegion":"四川省绵阳市涪城区"},{"code":510704,"parentCode":510700,"name":"游仙区","path":"/510000/510700/510704","hasChildren":false,"fullRegion":"四川省绵阳市游仙区"},{"code":510705,"parentCode":510700,"name":"安州区","path":"/510000/510700/510705","hasChildren":false,"fullRegion":"四川省绵阳市安州区"},{"code":510722,"parentCode":510700,"name":"三台县","path":"/510000/510700/510722","hasChildren":false,"fullRegion":"四川省绵阳市三台县"},{"code":510723,"parentCode":510700,"name":"盐亭县","path":"/510000/510700/510723","hasChildren":false,"fullRegion":"四川省绵阳市盐亭县"},{"code":510725,"parentCode":510700,"name":"梓潼县","path":"/510000/510700/510725","hasChildren":false,"fullRegion":"四川省绵阳市梓潼县"},{"code":510726,"parentCode":510700,"name":"北川羌族自治县","path":"/510000/510700/510726","hasChildren":false,"fullRegion":"四川省绵阳市北川羌族自治县"},{"code":510727,"parentCode":510700,"name":"平武县","path":"/510000/510700/510727","hasChildren":false,"fullRegion":"四川省绵阳市平武县"},{"code":510781,"parentCode":510700,"name":"江油市","path":"/510000/51070
2025-08-28 16:38:21.565 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  510800/510822","hasChildren":false,"fullRegion":"四川省广元市青川县"},{"code":510823,"parentCode":510800,"name":"剑阁县","path":"/510000/510800/510823","hasChildren":false,"fullRegion":"四川省广元市剑阁县"},{"code":510824,"parentCode":510800,"name":"苍溪县","path":"/510000/510800/510824","hasChildren":false,"fullRegion":"四川省广元市苍溪县"},{"code":510903,"parentCode":510900,"name":"船山区","path":"/510000/510900/510903","hasChildren":false,"fullRegion":"四川省遂宁市船山区"},{"code":510904,"parentCode":510900,"name":"安居区","path":"/510000/510900/510904","hasChildren":false,"fullRegion":"四川省遂宁市安居区"},{"code":510921,"parentCode":510900,"name":"蓬溪县","path":"/510000/510900/510921","hasChildren":false,"fullRegion":"四川省遂宁市蓬溪县"},{"code":510923,"parentCode":510900,"name":"大英县","path":"/510000/510900/510923","hasChildren":false,"fullRegion":"四川省遂宁市大英县"},{"code":510981,"parentCode":510900,"name":"射洪市","path":"/510000/510900/510981","hasChildren":false,"fullRegion":"四川省遂宁市射洪市"},{"code":511002,"parentCode":511000,"name":"市中区","path":"/510000/511000/511002","hasChildren":false,"fullRegion":"四川省内江市市中区"},{"code":511011,"parentCode":511000,"name":"东兴区","path":"/510000/511000/511011","hasChildren":false,"fullRegion":"四川省内江市东兴区"},{"code":511024,"parentCode":511000,"name":"威远县","path":"/510000/511000/511024","hasChildren":false,"fullRegion":"四川省内江市威远县"},{"code":511025,"parentCode":511000,"name":"资中县","path":"/510000/511000/511025","hasChildren":false,"fullRegion":"四川省内江市资中县"},{"code":511083,"parentCode":511000,"name":"隆昌市","path":"/510000/511000/511083","hasChildren":false,"fullRegion":"四川省内江市隆昌市"},{"code":511102,"parentCode":511100,"name":"市中区","path":"/510000/511100/511102","hasChildren":false,"fullRegion":"四川省乐山市市中区"},{"code":511111,"parentCode":511100,"name":"沙湾区","path":"/510000/511100/511111","hasChildren":false,"fullRegion":"四川省乐山市沙湾区"},{"code":511112,"parentCode":511100,"name":"五通桥区","path":"/510000/511100/511112","hasChildren":false,"fullRegion":"四川省乐山市五通桥区"},{"code":511113,"parentCode":511100,"name":"金口河区","path":"/510000/511100/511113","hasChildren":false,"fullRegion":"四川省乐山市金口河区"},{"code":511123,"parentCode":511100,"name":"犍为县","path":"/510000/511100/511123","hasChildren":false,"fullRegion":"四川省乐山市犍为县"},{"code":511124,"parentCode":511100,"name":"井研县","path":"/510000/511100/511124","hasChildren":false,"fullRegion":"四川省乐山市井研县"},{"code":511126,"parentCode":511100,"name":"夹江县","path":"/510000/511100/511126","hasChildren":false,"fullRegion":"四川省乐山市夹江县"},{"code":511129,"parentCode":511100,"name":"沐川县","path":"/510000/511100/511129","hasChildren":false,"fullRegion":"四川省乐山市沐川县"},{"code":511132,"parentCode":511100,"name":"峨边彝族自治县","path":"/510000/511100/511132","hasChildren":false,"fullRegion":"四川省乐山市峨边彝族自治县"},{"code":511133,"parentCode":511100,"name":"马边彝族自治县","path":"/510000/511100/511133","hasChildren":false,"fullRegion":"四川省乐山市马边彝族自治县"},{"code":511181,"parentCode":511100,"name":"峨眉山市","path":"/510000/511100/511181","hasChildren":false,"fullRegion":"四川省乐山市峨眉山市"},{"code":511302,"parentCode":511300,"name":"顺庆区","path":"/510000/511300/511302","hasChildren":false,"fullRegion":"四川省南充市顺庆区"},{"code":511303,"parentCode":511300,"name":"高坪区","path":"/510000/511300/511303","hasChildren":false,"fullRegion":"四川省南充市高坪区"},{"code":511304,"parentCode":511300,"name":"嘉陵区","path":"/510000/511300/511304","hasChildren":false,"fullRegion":"四川省南充市嘉陵区"},{"code":511321,"parentCode"
2025-08-28 16:38:21.565 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  "阆中市","path":"/510000/511300/511381","hasChildren":false,"fullRegion":"四川省南充市阆中市"},{"code":511402,"parentCode":511400,"name":"东坡区","path":"/510000/511400/511402","hasChildren":false,"fullRegion":"四川省眉山市东坡区"},{"code":511403,"parentCode":511400,"name":"彭山区","path":"/510000/511400/511403","hasChildren":false,"fullRegion":"四川省眉山市彭山区"},{"code":511421,"parentCode":511400,"name":"仁寿县","path":"/510000/511400/511421","hasChildren":false,"fullRegion":"四川省眉山市仁寿县"},{"code":511423,"parentCode":511400,"name":"洪雅县","path":"/510000/511400/511423","hasChildren":false,"fullRegion":"四川省眉山市洪雅县"},{"code":511424,"parentCode":511400,"name":"丹棱县","path":"/510000/511400/511424","hasChildren":false,"fullRegion":"四川省眉山市丹棱县"},{"code":511425,"parentCode":511400,"name":"青神县","path":"/510000/511400/511425","hasChildren":false,"fullRegion":"四川省眉山市青神县"},{"code":511502,"parentCode":511500,"name":"翠屏区","path":"/510000/511500/511502","hasChildren":false,"fullRegion":"四川省宜宾市翠屏区"},{"code":511503,"parentCode":511500,"name":"南溪区","path":"/510000/511500/511503","hasChildren":false,"fullRegion":"四川省宜宾市南溪区"},{"code":511504,"parentCode":511500,"name":"叙州区","path":"/510000/511500/511504","hasChildren":false,"fullRegion":"四川省宜宾市叙州区"},{"code":511523,"parentCode":511500,"name":"江安县","path":"/510000/511500/511523","hasChildren":false,"fullRegion":"四川省宜宾市江安县"},{"code":511524,"parentCode":511500,"name":"长宁县","path":"/510000/511500/511524","hasChildren":false,"fullRegion":"四川省宜宾市长宁县"},{"code":511525,"parentCode":511500,"name":"高县","path":"/510000/511500/511525","hasChildren":false,"fullRegion":"四川省宜宾市高县"},{"code":511526,"parentCode":511500,"name":"珙县","path":"/510000/511500/511526","hasChildren":false,"fullRegion":"四川省宜宾市珙县"},{"code":511527,"parentCode":511500,"name":"筠连县","path":"/510000/511500/511527","hasChildren":false,"fullRegion":"四川省宜宾市筠连县"},{"code":511528,"parentCode":511500,"name":"兴文县","path":"/510000/511500/511528","hasChildren":false,"fullRegion":"四川省宜宾市兴文县"},{"code":511529,"parentCode":511500,"name":"屏山县","path":"/510000/511500/511529","hasChildren":false,"fullRegion":"四川省宜宾市屏山县"},{"code":511602,"parentCode":511600,"name":"广安区","path":"/510000/511600/511602","hasChildren":false,"fullRegion":"四川省广安市广安区"},{"code":511603,"parentCode":511600,"name":"前锋区","path":"/510000/511600/511603","hasChildren":false,"fullRegion":"四川省广安市前锋区"},{"code":511621,"parentCode":511600,"name":"岳池县","path":"/510000/511600/511621","hasChildren":false,"fullRegion":"四川省广安市岳池县"},{"code":511622,"parentCode":511600,"name":"武胜县","path":"/510000/511600/511622","hasChildren":false,"fullRegion":"四川省广安市武胜县"},{"code":511623,"parentCode":511600,"name":"邻水县","path":"/510000/511600/511623","hasChildren":false,"fullRegion":"四川省广安市邻水县"},{"code":511681,"parentCode":511600,"name":"华蓥市","path":"/510000/511600/511681","hasChildren":false,"fullRegion":"四川省广安市华蓥市"},{"code":511702,"parentCode":511700,"name":"通川区","path":"/510000/511700/511702","hasChildren":false,"fullRegion":"四川省达州市通川区"},{"code":511703,"parentCode":511700,"name":"达川区","path":"/510000/511700/511703","hasChildren":false,"fullRegion":"四川省达州市达川区"},{"code":511722,"parentCode":511700,"name":"宣汉县","path":"/510000/511700/511722","hasChildren":false,"fullRegion":"四川省达州市宣汉县"},{"code":511723,"parentCode":511700,"name":"开江县","path":"/510000/511700/511723","hasChildren":false,"fullRegion":"四川省达州市开江县"},{"code":511724,"parentCode":511700,"name":"大竹县","path":"/510000/511700/
2025-08-28 16:38:21.565 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  "path":"/510000/511800/511822","hasChildren":false,"fullRegion":"四川省雅安市荥经县"},{"code":511823,"parentCode":511800,"name":"汉源县","path":"/510000/511800/511823","hasChildren":false,"fullRegion":"四川省雅安市汉源县"},{"code":511824,"parentCode":511800,"name":"石棉县","path":"/510000/511800/511824","hasChildren":false,"fullRegion":"四川省雅安市石棉县"},{"code":511825,"parentCode":511800,"name":"天全县","path":"/510000/511800/511825","hasChildren":false,"fullRegion":"四川省雅安市天全县"},{"code":511826,"parentCode":511800,"name":"芦山县","path":"/510000/511800/511826","hasChildren":false,"fullRegion":"四川省雅安市芦山县"},{"code":511827,"parentCode":511800,"name":"宝兴县","path":"/510000/511800/511827","hasChildren":false,"fullRegion":"四川省雅安市宝兴县"},{"code":511902,"parentCode":511900,"name":"巴州区","path":"/510000/511900/511902","hasChildren":false,"fullRegion":"四川省巴中市巴州区"},{"code":511903,"parentCode":511900,"name":"恩阳区","path":"/510000/511900/511903","hasChildren":false,"fullRegion":"四川省巴中市恩阳区"},{"code":511921,"parentCode":511900,"name":"通江县","path":"/510000/511900/511921","hasChildren":false,"fullRegion":"四川省巴中市通江县"},{"code":511922,"parentCode":511900,"name":"南江县","path":"/510000/511900/511922","hasChildren":false,"fullRegion":"四川省巴中市南江县"},{"code":511923,"parentCode":511900,"name":"平昌县","path":"/510000/511900/511923","hasChildren":false,"fullRegion":"四川省巴中市平昌县"},{"code":512002,"parentCode":512000,"name":"雁江区","path":"/510000/512000/512002","hasChildren":false,"fullRegion":"四川省资阳市雁江区"},{"code":512021,"parentCode":512000,"name":"安岳县","path":"/510000/512000/512021","hasChildren":false,"fullRegion":"四川省资阳市安岳县"},{"code":512022,"parentCode":512000,"name":"乐至县","path":"/510000/512000/512022","hasChildren":false,"fullRegion":"四川省资阳市乐至县"},{"code":513201,"parentCode":513200,"name":"马尔康市","path":"/510000/513200/513201","hasChildren":false,"fullRegion":"四川省阿坝藏族羌族自治州马尔康市"},{"code":513221,"parentCode":513200,"name":"汶川县","path":"/510000/513200/513221","hasChildren":false,"fullRegion":"四川省阿坝藏族羌族自治州汶川县"},{"code":513222,"parentCode":513200,"name":"理县","path":"/510000/513200/513222","hasChildren":false,"fullRegion":"四川省阿坝藏族羌族自治州理县"},{"code":513223,"parentCode":513200,"name":"茂县","path":"/510000/513200/513223","hasChildren":false,"fullRegion":"四川省阿坝藏族羌族自治州茂县"},{"code":513224,"parentCode":513200,"name":"松潘县","path":"/510000/513200/513224","hasChildren":false,"fullRegion":"四川省阿坝藏族羌族自治州松潘县"},{"code":513225,"parentCode":513200,"name":"九寨沟县","path":"/510000/513200/513225","hasChildren":false,"fullRegion":"四川省阿坝藏族羌族自治州九寨沟县"},{"code":513226,"parentCode":513200,"name":"金川县","path":"/510000/513200/513226","hasChildren":false,"fullRegion":"四川省阿坝藏族羌族自治州金川县"},{"code":513227,"parentCode":513200,"name":"小金县","path":"/510000/513200/513227","hasChildren":false,"fullRegion":"四川省阿坝藏族羌族自治州小金县"},{"code":513228,"parentCode":513200,"name":"黑水县","path":"/510000/513200/513228","hasChildren":false,"fullRegion":"四川省阿坝藏族羌族自治州黑水县"},{"code":513230,"parentCode":513200,"name":"壤塘县","path":"/510000/513200/513230","hasChildren":false,"fullRegion":"四川省阿坝藏族羌族自治州壤塘县"},{"code":513231,"parentCode":513200,"name":"阿坝县","path":"/510000/513200/513231","hasChildren":false,"fullRegion":"四川省阿坝藏族羌族自治州阿坝县"},{"code":513232,"parentCode":513200,"name":"若尔盖县","path":"/510000/513200/513232","hasChildren":false,"fullRegion":"四川省阿坝藏族羌族自治州若尔盖县"},{"co
2025-08-28 16:38:21.565 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  ","hasChildren":false,"fullRegion":"四川省甘孜藏族自治州雅江县"},{"code":513326,"parentCode":513300,"name":"道孚县","path":"/510000/513300/513326","hasChildren":false,"fullRegion":"四川省甘孜藏族自治州道孚县"},{"code":513327,"parentCode":513300,"name":"炉霍县","path":"/510000/513300/513327","hasChildren":false,"fullRegion":"四川省甘孜藏族自治州炉霍县"},{"code":513328,"parentCode":513300,"name":"甘孜县","path":"/510000/513300/513328","hasChildren":false,"fullRegion":"四川省甘孜藏族自治州甘孜县"},{"code":513329,"parentCode":513300,"name":"新龙县","path":"/510000/513300/513329","hasChildren":false,"fullRegion":"四川省甘孜藏族自治州新龙县"},{"code":513330,"parentCode":513300,"name":"德格县","path":"/510000/513300/513330","hasChildren":false,"fullRegion":"四川省甘孜藏族自治州德格县"},{"code":513331,"parentCode":513300,"name":"白玉县","path":"/510000/513300/513331","hasChildren":false,"fullRegion":"四川省甘孜藏族自治州白玉县"},{"code":513332,"parentCode":513300,"name":"石渠县","path":"/510000/513300/513332","hasChildren":false,"fullRegion":"四川省甘孜藏族自治州石渠县"},{"code":513333,"parentCode":513300,"name":"色达县","path":"/510000/513300/513333","hasChildren":false,"fullRegion":"四川省甘孜藏族自治州色达县"},{"code":513334,"parentCode":513300,"name":"理塘县","path":"/510000/513300/513334","hasChildren":false,"fullRegion":"四川省甘孜藏族自治州理塘县"},{"code":513335,"parentCode":513300,"name":"巴塘县","path":"/510000/513300/513335","hasChildren":false,"fullRegion":"四川省甘孜藏族自治州巴塘县"},{"code":513336,"parentCode":513300,"name":"乡城县","path":"/510000/513300/513336","hasChildren":false,"fullRegion":"四川省甘孜藏族自治州乡城县"},{"code":513337,"parentCode":513300,"name":"稻城县","path":"/510000/513300/513337","hasChildren":false,"fullRegion":"四川省甘孜藏族自治州稻城县"},{"code":513338,"parentCode":513300,"name":"得荣县","path":"/510000/513300/513338","hasChildren":false,"fullRegion":"四川省甘孜藏族自治州得荣县"},{"code":513401,"parentCode":513400,"name":"西昌市","path":"/510000/513400/513401","hasChildren":false,"fullRegion":"四川省凉山彝族自治州西昌市"},{"code":513402,"parentCode":513400,"name":"会理市","path":"/510000/513400/513402","hasChildren":false,"fullRegion":"四川省凉山彝族自治州会理市"},{"code":513422,"parentCode":513400,"name":"木里藏族自治县","path":"/510000/513400/513422","hasChildren":false,"fullRegion":"四川省凉山彝族自治州木里藏族自治县"},{"code":513423,"parentCode":513400,"name":"盐源县","path":"/510000/513400/513423","hasChildren":false,"fullRegion":"四川省凉山彝族自治州盐源县"},{"code":513424,"parentCode":513400,"name":"德昌县","path":"/510000/513400/513424","hasChildren":false,"fullRegion":"四川省凉山彝族自治州德昌县"},{"code":513426,"parentCode":513400,"name":"会东县","path":"/510000/513400/513426","hasChildren":false,"fullRegion":"四川省凉山彝族自治州会东县"},{"code":513427,"parentCode":513400,"name":"宁南县","path":"/510000/513400/513427","hasChildren":false,"fullRegion":"四川省凉山彝族自治州宁南县"},{"code":513428,"parentCode":513400,"name":"普格县","path":"/510000/513400/513428","hasChildren":false,"fullRegion":"四川省凉山彝族自治州普格县"},{"code":513429,"parentCode":513400,"name":"布拖县","path":"/510000/513400/513429","hasChildren":false,"fullRegion":"四川省凉山彝族自治州布拖县"},{"code":513430,"parentCode":513400,"name":"金阳县","path":"/510000/513400/513430","hasChildren":false,"fullRegion":"四川省凉山彝族自治州金阳县"},{"code":513431,"parentCode":513400,"name":"昭觉县","path":"/510000/513400/513431","hasChildren":false,"fullRegion":"四川省凉山彝族自治州昭觉县"},{"code":513432,"parentCode":513400,"name":"喜德县","path":"/510000/513400/513
2025-08-28 16:38:21.565 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  h":"/510000/510100/510108","hasChildren":false,"fullRegion":"四川省成都市成华区"}}}
2025-08-28 16:38:21.565 11783-11974 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (33360-byte body)
2025-08-28 16:38:21.574 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  工程师详情响应: {engineerInfo={id=1901815444382265345, introduce=维修学徒, yearOfService=0.0, regionCode=[510104.0, 510105.0, 510106.0, 510107.0, 510108.0, 510112.0, 510113.0, 510114.0, 510115.0, 510116.0, 510117.0, 510118.0, 510121.0, 510129.0, 510131.0, 510181.0, 510182.0, 510183.0, 510184.0, 510185.0, 510302.0, 510303.0, 510304.0, 510311.0, 510321.0, 510322.0, 510402.0, 510403.0, 510411.0, 510421.0, 510422.0, 510502.0, 510503.0, 510504.0, 510521.0, 510522.0, 510524.0, 510525.0, 510603.0, 510604.0, 510623.0, 510681.0, 510682.0, 510683.0, 510703.0, 510704.0, 510705.0, 510722.0, 510723.0, 510725.0, 510726.0, 510727.0, 510781.0, 510802.0, 510811.0, 510812.0, 510821.0, 510822.0, 510823.0, 510824.0, 510903.0, 510904.0, 510921.0, 510923.0, 510981.0, 511002.0, 511011.0, 511024.0, 511025.0, 511083.0, 511102.0, 511111.0, 511112.0, 511113.0, 511123.0, 511124.0, 511126.0, 511129.0, 511132.0, 511133.0, 511181.0, 511302.0, 511303.0, 511304.0, 511321.0, 511322.0, 511323.0, 511324.0, 511325.0, 511381.0, 511402.0, 511403.0, 511421.0, 511423.0, 511424.0, 511425.0, 511502.0, 511503.0, 511504.0, 511523.0, 511524.0, 511525.0, 511526.0, 511527.0, 511528.0, 511529.0, 511602.0, 511603.0, 511621.0, 511622.0, 511623.0, 511681.0, 511702.0, 511703.0, 511722.0, 511723.0, 511724.0, 511725.0, 511781.0, 511802.0, 511803.0, 511822.0, 511823.0, 511824.0, 511825.0, 511826.0, 511827.0, 511902.0, 511903.0, 511921.0, 511922.0, 511923.0, 512002.0, 512021.0, 512022.0, 513201.0, 513221.0, 513222.0, 513223.0, 513224.0, 513225.0, 513226.0, 513227.0, 513228.0, 513230.0, 513231.0, 513232.0, 513233.0, 513301.0, 513322.0, 513323.0, 513324.0, 513325.0, 513326.0, 513327.0, 513328.0, 513329.0, 513330.0, 513331.0, 513332.0, 513333.0, 513334.0, 513335.0, 513336.0, 513337.0, 513338.0, 513401.0, 513402.0, 513422.0, 513423.0, 513424.0, 513426.0, 513427.0, 513428.0, 513429.0, 513430.0, 513431.0, 513432.0, 513433.0, 513434.0, 513435.0, 513436.0, 513437.0], locationRegionCode=510108.0}, userManageVo={id=1901815444382265345, code=SCZJZY0027, name=舒茂林, mobileNumber=17321858976, identityCardNumber=511028199906092916, sex={value=male, label=男}, type={value=permanent, label=内部用户}, state={value=normal, label=正常}, isBuildIn=false}, engineerSkills=[{productId=1730431526003167490, brand=理光, serial=IM C6000系列, skillExp={value=5001, label=普通}}, {productId=1730431526003167496, brand=理光, serial=MP C6004系列, skillExp={value=5001, label=普通}}, {productId=1730431526003167503, brand=理光, serial=MP C3504系列, skillExp={value=5001, label=普通}}, {productId=1730431526003167515, brand=理光, serial=MP C2503系列, skillExp={value=5001, label=普通}}, {productId=1730431526003167520, brand=理光, serial=MP C2504系列, skillExp={value=5001, label=普通}}, {productId=1730431526003167525, brand=理光, serial=MP C6003系列, skillExp={value=5001, label=普通}}, {productId=1730431526003167529, brand=理光, serial=MP C3503系列, skillExp={value=5001, label=普通}}, {productId=1730431526011556127, brand=理光, serial=Pro C5200系列, skillExp={value=5001, label=普通}}, {productId=1730431526003167415, brand=理光, serial=MP 7500系列, skillExp={value=5001, label=普通}}, {productId=1730431526003167419, brand=理光, serial=MP 7503系列, skillExp={value=5001, label=普通}}, {productId=1730431526003167426, brand=理光, serial=MP 7001系列, skillExp={value=5001, label=普通}}, {productId=1730431526003167431, brand=理光, serial=MP 7000系列, skillExp={value=5001, label=普通}}, {productId=1730431526003167435, brand=理光, serial=IM 8000系列, skillExp={value=5001, label=普通}}, {productId=1730431526003167439, brand=理光, serial=MP 7502系列, skillExp={value=5001, label=普通}}, {productId=1730431526011556057, brand=理光, serial=MP 6055系列, skillExp={value=5001, label=普通}}, {productId=1730431526011556064, brand=理光, serial=MP 6054系列, skillExp={value=5001, label=普通}}, {productId=1730431526011556071, brand=理光, serial=MP 3554系列, skillExp={value=5001
2025-08-28 16:38:21.574 11783-11783 WorkOrderDetail         com.example.repairorderapp           D  从Map中解析到电话: 17321858976
