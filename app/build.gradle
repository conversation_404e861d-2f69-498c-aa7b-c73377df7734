plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
}

android {
    namespace 'com.example.repairorderapp'
    compileSdk 34
    
    packaging {
        resources {
            excludes += ['META-INF/INDEX.LIST', 'META-INF/DEPENDENCIES']
        }
    }

    defaultConfig {
        applicationId "com.example.repairorderapp"
        minSdk 26
        targetSdk 34
        versionCode 1
        versionName "1.0.3"
        
        // 启用MultiDex支持
        multiDexEnabled true

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        
        // Room数据库相关配置
        javaCompileOptions {
            annotationProcessorOptions {
                arguments += [
                    "room.schemaLocation": "$projectDir/schemas".toString(),
                    "room.incremental"   : "true"
                ]
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled true        // 启用代码缩减
            shrinkResources true      // 启用资源缩减
            zipAlignEnabled true      // 优化APK文件
            debuggable false          // 禁用调试
            jniDebuggable false       // 禁用JNI调试
            renderscriptDebuggable false // 禁用RenderScript调试
            pseudoLocalesEnabled false // 禁用伪区域设置
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            // applicationIdSuffix ".debug" // 为调试版本添加后缀，便于区分
            versionNameSuffix "-debug"  // 为调试版本添加版本名后缀
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    
    // 启用ViewBinding和DataBinding
    buildFeatures {
        viewBinding true
        dataBinding true
        buildConfig true  // 启用BuildConfig生成
    }
    
    // 添加lint配置以解决构建问题
    lintOptions {
        abortOnError false // 不要因为lint错误中断构建
        checkReleaseBuilds false // 不在release构建时检查
    }
}

dependencies {
    // 添加MultiDex支持 - 确保放在前面
    implementation 'androidx.multidex:multidex:2.0.1'
    
    // 核心Android依赖
    implementation 'androidx.core:core:1.12.0'
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    
    // 添加AndroidX 核心组件支持
    implementation 'androidx.activity:activity:1.8.2'
    implementation 'androidx.activity:activity-ktx:1.8.2'
    implementation 'androidx.fragment:fragment:1.6.2'
    implementation 'androidx.fragment:fragment-ktx:1.6.2'
    
    // SwipeRefreshLayout
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    // ViewPager2
    implementation 'androidx.viewpager2:viewpager2:1.0.0'
    
    // 生命周期组件
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    
    // 导航组件
    implementation 'androidx.navigation:navigation-fragment-ktx:2.7.6'
    implementation 'androidx.navigation:navigation-ui-ktx:2.7.6'
    
    // Room数据库相关依赖
    implementation 'androidx.room:room-runtime:2.6.1'
    kapt 'androidx.room:room-compiler:2.6.1'
    implementation 'androidx.room:room-ktx:2.6.1' // Kotlin扩展
    
    // Retrofit2相关依赖 (升级到最新稳定版本)
    implementation 'com.squareup.retrofit2:retrofit:2.11.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.11.0'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    
    // Moshi JSON解析库依赖 (升级到最新版本)
    implementation 'com.squareup.moshi:moshi:1.15.0'
    implementation 'com.squareup.moshi:moshi-kotlin:1.15.0'
    implementation 'com.squareup.retrofit2:converter-moshi:2.11.0'
    
    // 协程依赖
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-reactive:1.7.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3'
    
    // WorkManager依赖
    implementation 'androidx.work:work-runtime-ktx:2.9.0'
    
    // 腾讯地图SDK依赖
    //implementation 'com.tencent.map:tencent-map-vector-sdk:4.3.9'
    implementation 'com.tencent.map:tencent-map-vector-sdk:5.7.0'
    implementation 'com.tencent.map.geolocation:TencentLocationSdk-openplatform:7.4.9'
    
    // 签名面板库
    implementation 'com.github.gcacace:signature-pad:1.3.1'
    
    // SM2加密库
    implementation 'org.bouncycastle:bcprov-jdk15on:1.70'
    
    // 图片加载库
    implementation 'com.github.bumptech.glide:glide:4.14.2'
    kapt 'com.github.bumptech.glide:compiler:4.14.2'
    
    // 图片查看库
    implementation 'com.github.chrisbanes:PhotoView:2.3.0'
    
    // 测试相关
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'

    // Matisse图片选择库
    implementation 'com.github.zhihu:Matisse:0.5.3-beta3'

    // 腾讯云 COS SDK (标准版)
    implementation 'com.qcloud.cos:cos-android:5.9.+'

    // Material Design组件依赖
    implementation 'com.google.android.material:material:1.10.0'

    // JSON处理
    implementation 'com.google.code.gson:gson:2.10.1'
    
    // ZXing二维码扫描库
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'
    implementation 'com.google.zxing:core:3.4.1'
    
    // 添加缺失的androidx.core依赖
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.core:core:1.12.0'
    implementation 'androidx.annotation:annotation:1.7.1'
}