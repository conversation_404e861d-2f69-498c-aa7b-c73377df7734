package com.example.repairorderapp.util

import android.app.Activity
import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import android.util.Log
import androidx.annotation.RequiresApi

/**
 * 电池优化辅助工具
 * 用于检查和引导用户设置电池优化白名单
 */
object BatteryOptimizationHelper {
    private const val TAG = "BatteryOptHelper"
    
    /**
     * 检查应用是否在电池优化白名单中
     */
    fun isIgnoringBatteryOptimizations(context: Context): Boolean {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            return powerManager.isIgnoringBatteryOptimizations(context.packageName)
        }
        return true // M以下版本默认返回true
    }
    
    /**
     * 显示电池优化设置对话框
     */
    fun showBatteryOptimizationDialog(activity: Activity) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            return
        }

        // 检查Activity状态，避免在Activity销毁时显示对话框
        if (activity.isFinishing || activity.isDestroyed) {
            Log.d("BatteryOptimizationHelper", "Activity已销毁或正在销毁，跳过对话框显示")
            return
        }

        if (!isIgnoringBatteryOptimizations(activity)) {
            try {
                AlertDialog.Builder(activity)
                    .setTitle("优化应用性能")
                    .setMessage("为确保应用功能正常运行，建议您将本应用加入电池优化白名单。\n\n" +
                              "这将允许应用：\n" +
                              "• 及时接收工单通知\n" +
                              "• 保持稳定的网络连接\n" +
                              "• 提供更流畅的使用体验")
                    .setPositiveButton("前往设置") { _, _ ->
                        requestBatteryOptimizationExemption(activity)
                    }
                    .setNegativeButton("稍后再说", null)
                    .setCancelable(false)
                    .show()
            } catch (e: Exception) {
                Log.e("BatteryOptimizationHelper", "显示电池优化对话框失败", e)
            }
        }
    }
    
    /**
     * 请求电池优化豁免
     */
    @RequiresApi(Build.VERSION_CODES.M)
    private fun requestBatteryOptimizationExemption(activity: Activity) {
        try {
            val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                data = Uri.parse("package:${activity.packageName}")
            }
            activity.startActivity(intent)
            Log.d(TAG, "已打开电池优化设置")
        } catch (e: Exception) {
            Log.e(TAG, "打开电池优化设置失败: ${e.message}")
            // 如果直接打开失败，尝试打开电池优化列表
            openBatteryOptimizationSettings(activity)
        }
    }
    
    /**
     * 打开电池优化设置列表
     */
    private fun openBatteryOptimizationSettings(activity: Activity) {
        try {
            val intent = Intent(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS)
            activity.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "打开电池优化列表失败: ${e.message}")
            // 最后尝试打开应用详情
            openAppDetailsSettings(activity)
        }
    }
    
    /**
     * 打开应用详情设置
     */
    private fun openAppDetailsSettings(activity: Activity) {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.parse("package:${activity.packageName}")
            }
            activity.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "打开应用详情失败: ${e.message}")
        }
    }
    
    /**
     * 显示厂商特定的优化提示
     */
//    fun showVendorSpecificTips(activity: Activity) {
//        val manufacturer = Build.MANUFACTURER.lowercase()
//        val tips = when {
//            manufacturer.contains("xiaomi") || manufacturer.contains("redmi") -> {
//                "MIUI系统设置建议：\n\n" +
//                "1. 设置 > 电池与性能 > 应用电池优化\n" +
//                "2. 找到本应用，选择\"无限制\"\n" +
//                "3. 开启\"自启动\"权限\n" +
//                "4. 锁定应用在后台（最近任务中下拉应用卡片）"
//            }
//            manufacturer.contains("huawei") || manufacturer.contains("honor") -> {
//                "EMUI系统设置建议：\n\n" +
//                "1. 设置 > 电池 > 启动管理\n" +
//                "2. 找到本应用，关闭\"自动管理\"\n" +
//                "3. 开启\"允许自启动\"、\"允许关联启动\"、\"允许后台活动\"\n" +
//                "4. 锁定应用在后台"
//            }
//            manufacturer.contains("oppo") || manufacturer.contains("realme") -> {
//                "ColorOS系统设置建议：\n\n" +
//                "1. 设置 > 电池 > 应用耗电管理\n" +
//                "2. 找到本应用，开启\"允许后台运行\"\n" +
//                "3. 开启\"自启动\"权限\n" +
//                "4. 关闭\"后台冻结\"和\"应用速冻\""
//            }
//            manufacturer.contains("vivo") -> {
//                "FuntouchOS系统设置建议：\n\n" +
//                "1. 设置 > 电池 > 后台高耗电\n" +
//                "2. 找到本应用，开启\"允许后台高耗电\"\n" +
//                "3. 开启\"自启动\"权限\n" +
//                "4. 将应用加入白名单"
//            }
//            else -> null
//        }
//
//        if (tips != null) {
//            AlertDialog.Builder(activity)
//                .setTitle("${Build.MANUFACTURER}系统优化建议")
//                .setMessage(tips)
//                .setPositiveButton("知道了", null)
//                .show()
//        }
//    }
} 