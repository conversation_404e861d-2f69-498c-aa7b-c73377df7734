package com.example.repairorderapp.ui.launch

import android.Manifest
import android.app.AlertDialog
import android.app.AlarmManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.example.repairorderapp.MainActivity
import com.example.repairorderapp.R
import com.example.repairorderapp.RepairOrderApp
import com.example.repairorderapp.data.repository.LearnRepository
import com.example.repairorderapp.ui.login.LoginActivity
import com.example.repairorderapp.ui.setting.KeepAliveGuideActivity
import com.example.repairorderapp.util.SharedPrefsManager
import com.example.repairorderapp.network.TokenFixer
import com.example.repairorderapp.network.TokenManager
import com.example.repairorderapp.network.ApiClient
import com.example.repairorderapp.performance.PerformanceMonitor
import com.example.repairorderapp.utils.PermissionUtils
import okhttp3.internal.notify
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject

/**
 * 启动页，负责检查登录状态和位置权限
 */
class SplashActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "SplashActivity"
        private const val PERMISSION_REQUEST_CODE = 1001
        private const val BACKGROUND_LOCATION_REQUEST_CODE = 1002
        private const val SETTINGS_REQUEST_CODE = 1003
        
        // 需要请求的权限
        private val REQUIRED_PERMISSIONS = arrayOf(
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
        ).let { base ->
            // Android 13+ 需新增通知权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                base + Manifest.permission.POST_NOTIFICATIONS
            } else {
                base
            }
        }
        
        // Android 10及以上需要的后台位置权限
        private val BACKGROUND_LOCATION_PERMISSION = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            Manifest.permission.ACCESS_BACKGROUND_LOCATION
        } else {
            ""
        }
        
        // Android 13+ 通知权限
        private val NOTIFICATION_PERMISSION = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Manifest.permission.POST_NOTIFICATIONS
        } else {
            ""
        }
    }
    
    private lateinit var progressBar: ProgressBar
    private lateinit var messageText: TextView
    private lateinit var permissionButton: Button
    private var permissionDeniedCount = 0
    private var locationServicePromptCount = 0
    
    // 保存对话框引用，便于在需要时关闭
    private var activeDialog: AlertDialog? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 开始应用启动性能监控
        PerformanceMonitor.startTiming("app_startup", mapOf(
            "activity" to "SplashActivity",
            "isFirstLaunch" to isFirstLaunch()
        ))

        setContentView(R.layout.activity_splash)

        // 设置启动页面标志，用于控制会话过期弹窗
        RepairOrderApp.setInSplashActivity(true)
        
        // 初始化视图
        progressBar = findViewById(R.id.progress_bar)
        messageText = findViewById(R.id.message_text)
        permissionButton = findViewById(R.id.permission_button)
        
        // 设置权限请求按钮
        permissionButton.setOnClickListener {
            requestLocationPermissions()
        }
        
        // 应用启动时清理知识库缓存
        clearLearnCache()
        
        // 检查位置权限
        checkPermissionsAndProceed()
    }

    /**
     * 检查是否为首次启动
     */
    private fun isFirstLaunch(): Boolean {
        val prefs = getSharedPreferences("app_prefs", MODE_PRIVATE)
        return prefs.getBoolean("is_first_launch", true)
    }

    override fun onDestroy() {
        // 重置启动页面标志
        RepairOrderApp.setInSplashActivity(false)
        
        // 在Activity销毁前关闭所有对话框，防止窗口泄漏
        dismissActiveDialog()
        
        // 确保TokenManager的对话框也被关闭，并设置静默模式避免弹窗
        try {
            com.example.repairorderapp.network.TokenManager.getInstance(applicationContext).dismissSessionExpiredDialog()
        } catch (e: Exception) {
            Log.e(TAG, "关闭TokenManager对话框时出错: ${e.message}", e)
        }
        
        super.onDestroy()
    }
    
    /**
     * 清理知识库相关的缓存和筛选状态
     */
    private fun clearLearnCache() {
        lifecycleScope.launch {
            try {
                // 使用一个独立的SharedPrefsManager实例
                val repository = LearnRepository(SharedPrefsManager(applicationContext))
                repository.clearAllKnowledgeBaseState()
                Log.d(TAG, "知识库缓存和状态已在应用启动时清除")
            } catch (e: Exception) {
                Log.e(TAG, "清理知识库缓存时出错", e)
            }
        }
    }
    
    /**
     * 关闭当前活动的对话框
     */
    private fun dismissActiveDialog() {
        activeDialog?.let {
            if (it.isShowing) {
                try {
                    it.dismiss()
                } catch (e: Exception) {
                    Log.e(TAG, "关闭对话框时出错: ${e.message}", e)
                }
            }
            activeDialog = null
        }
    }
    
    /**
     * 检查权限并继续
     */
    private fun checkPermissionsAndProceed() {
        // 显示加载提示
        progressBar.visibility = View.VISIBLE
        messageText.text = getString(R.string.checking_permissions)
        permissionButton.visibility = View.GONE
        
        // 延迟一点执行，让用户看到界面
        Handler(Looper.getMainLooper()).postDelayed({
            if (hasRequiredPermissions()) {
                // 已有权限，检查系统定位功能是否开启
                checkLocationServiceAndProceed()
            } else {
                // 没有权限，显示请求权限按钮
                showPermissionRequest()
            }
        }, 1000)
    }
    
    /**
     * 检查系统定位功能是否开启
     */
    private fun checkLocationServiceAndProceed() {
        if (!PermissionUtils.isLocationServiceEnabled(this)) {
            // 系统定位功能未开启，提示用户开启
            showLocationServiceDialog()
        } else {
            // 系统定位功能已开启，继续检查登录状态
            checkLoginAndNavigate()
        }
    }
    
    /**
     * 检查是否有所需的全部权限
     */
    private fun hasRequiredPermissions(): Boolean {
        for (permission in REQUIRED_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                return false
            }
        }
        
        // 检查通知权限（Android 13+）
        if (NOTIFICATION_PERMISSION.isNotEmpty() &&
            ContextCompat.checkSelfPermission(this, NOTIFICATION_PERMISSION) != PackageManager.PERMISSION_GRANTED) {
            return false
        }
        
        // 检查后台位置权限（Android 10+）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && 
            !PermissionUtils.hasBackgroundLocationPermission(this)) {
            return false
        }
        
        return true
    }
    
    /**
     * 显示请求权限界面
     */
    private fun showPermissionRequest() {
        progressBar.visibility = View.GONE
        messageText.text = getString(R.string.location_permission_required)
        permissionButton.visibility = View.VISIBLE
    }
    
    /**
     * 请求位置权限
     */
    private fun requestLocationPermissions() {
        // 先请求基本位置权限（包含通知权限）
        ActivityCompat.requestPermissions(
            this,
            REQUIRED_PERMISSIONS,
            PERMISSION_REQUEST_CODE
        )
    }
    
    /**
     * 请求后台位置权限（Android 10+）
     */
    private fun requestBackgroundLocationPermission() {
        // 直接请求后台位置权限，不跳转设置页面
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ActivityCompat.requestPermissions(
                this,
                arrayOf(Manifest.permission.ACCESS_BACKGROUND_LOCATION),
                BACKGROUND_LOCATION_REQUEST_CODE
            )
        }
    }
    
    /**
     * 检查登录状态并导航到相应页面
     */
    private fun checkLoginAndNavigate() {
        // 显示加载提示
        progressBar.visibility = View.VISIBLE
        messageText.text = getString(R.string.checking_login_status)
        permissionButton.visibility = View.GONE
        
        // 延迟一点执行，让用户看到界面
        Handler(Looper.getMainLooper()).postDelayed({
            // 检查Activity是否已结束
            if (isFinishing || isDestroyed) {
                return@postDelayed
            }
            
            val sharedPrefsManager = SharedPrefsManager(this)
            
            // 首先检查是否已登录
            if (sharedPrefsManager.getLoginStatus()) {
                // 检查并修复令牌问题
                val tokenFixer = TokenFixer.getInstance(this)
                val hasValidToken = tokenFixer.fixTokenIfNeeded()
                
                if (!hasValidToken) {
                    Log.e(TAG, "令牌检查失败，所有存储位置都没有有效令牌")
                    navigateToLoginActivity()
                    return@postDelayed
                }
                
                // 获取令牌并验证有效性
                val tokenPrefs = getSharedPreferences("token_pref", Context.MODE_PRIVATE)
                val token = tokenPrefs.getString("accessToken", "") ?: ""
                
                // accessToken 为空，直接要求重新登录（移除不可靠的静默刷新）
                if (token.isEmpty()) {
                    Log.w(TAG, "accessToken为空，需要重新登录")

                    // 清除所有Token相关数据
                    tokenPrefs.edit().clear().apply()
                    sharedPrefsManager.saveLoginStatus(false)

                    navigateToLoginActivity()
                    return@postDelayed
                }
                
                // 显示验证中提示
                progressBar.visibility = View.VISIBLE
                messageText.text = "正在验证登录信息..."
                
                // 优先使用网络验证令牌有效性
                TokenFixer.getInstance(this).verifyToken(token) { isValid ->
                    // 在UI线程中安全地处理回调结果
                    runOnUiThread {
                        // 再次检查Activity是否已结束
                        if (isFinishing || isDestroyed) {
                            return@runOnUiThread
                        }
                        
                        if (isValid) {
                            // 远程验证成功，确保登录状态正确设置
                            Log.d(TAG, "令牌远程验证成功，正在进入主页")
                            val tokenPrefs = getSharedPreferences("token_pref", Context.MODE_PRIVATE)
                            tokenPrefs.edit().putBoolean("isLogin", true).commit()

                            // 初始化COS SDK并进入主页
                            com.example.repairorderapp.RepairOrderApp.instance.initCosAfterLogin()

                            // 登录验证成功后上传设备信息
                            lifecycleScope.launch {
                                try {
                                    com.example.repairorderapp.manager.DeviceDataUploadManager.getInstance(this@SplashActivity)
                                        .uploadDeviceInfoOnLogin()
                                } catch (e: Exception) {
                                    Log.e(TAG, "验证成功后上传设备信息失败", e)
                                }
                            }

                            navigateToMainActivity()
                        } else {
                            // 远程验证失败，Token可能已过期，直接要求重新登录
                            Log.w(TAG, "令牌远程验证失败，Token可能已过期，需要重新登录")

                            // 清除过期的Token和登录状态
                            val tokenPrefs = getSharedPreferences("token_pref", Context.MODE_PRIVATE)
                            tokenPrefs.edit().clear().apply()
                            sharedPrefsManager.saveLoginStatus(false)

                            Log.i(TAG, "已清除过期Token，跳转到登录页面")
                            navigateToLoginActivity()
                        }
                    }
                }
            } else {
                // 未登录，跳转到登录页
                Log.w(TAG, "用户未登录，跳转到登录页面")
                navigateToLoginActivity()
            }
        }, 1000)
    }
    
    /**
     * 导航到主页
     */
    private fun navigateToMainActivity() {
        // 🔧 修复：日常启动时也要触发配置更新
        triggerConfigUpdateAfterLogin()

        // 结束应用启动性能监控
        PerformanceMonitor.endTiming("app_startup", mapOf(
            "success" to true,
            "destination" to "MainActivity"
        ))

        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        finish()
    }

    /**
     * 触发登录后的配置更新（适用于登录成功和日常启动）
     */
    private fun triggerConfigUpdateAfterLogin() {
        try {
            // 更新远程配置
            val remoteConfigManager = com.example.repairorderapp.manager.RemoteConfigManager.getInstance(this)
            remoteConfigManager.updateConfigAfterLogin()
            Log.d(TAG, "启动时开始更新远程配置")

            // 触发设备信息上传 - 使用lifecycleScope
            lifecycleScope.launch {
                try {
                    val deviceDataUploadManager = com.example.repairorderapp.manager.DeviceDataUploadManager.getInstance(applicationContext)
                    deviceDataUploadManager.uploadDeviceInfoOnLogin()
                    Log.i(TAG, "启动时设备信息上传已触发")
                } catch (e: Exception) {
                    Log.e(TAG, "启动时上传设备信息失败", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "启动时触发配置更新失败", e)
        }
    }
    
    /**
     * 导航到登录页
     */
    private fun navigateToLoginActivity() {
        // 结束应用启动性能监控
        PerformanceMonitor.endTiming("app_startup", mapOf(
            "success" to false,
            "destination" to "LoginActivity",
            "reason" to "authentication_required"
        ))

        val intent = Intent(this, LoginActivity::class.java)
        startActivity(intent)
        finish()
    }
    
    /**
     * 显示系统定位功能未开启的提醒对话框
     */
    private fun showLocationServiceDialog() {
        // 先关闭之前的对话框
        dismissActiveDialog()
        
        locationServicePromptCount++
        
        val title = if (locationServicePromptCount == 1) {
            "需要开启系统定位"
        } else {
            "仍需开启系统定位"
        }
        
        val message = if (locationServicePromptCount == 1) {
            "应用需要使用定位功能来提供位置相关服务，请在系统设置中开启定位功能后返回应用。"
        } else {
            "检测到系统定位功能仍未开启。请确保在设置中开启\"位置信息\"或\"定位服务\"，然后返回应用。"
        }
        
        val dialog = AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton("去开启") { _, _ ->
                // 打开系统定位设置页面
                val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
                startActivityForResult(intent, SETTINGS_REQUEST_CODE)
            }
            .setCancelable(false)
            .create()
        
        // 保存对话框引用
        activeDialog = dialog
        
        // 安全地显示对话框
        if (!isFinishing && !isDestroyed) {
            dialog.show()
        }
    }
    
    /**
     * 显示权限说明对话框（第一次被拒绝时）
     */
    private fun showPermissionExplanationDialog() {
        // 先关闭之前的对话框
        dismissActiveDialog()
        
        val message = """
            |维修工单应用需要位置权限才能正常工作。
            |
            |请点击"重新授权"并在权限设置中选择"始终允许"而不是"仅在使用应用时允许"。
        """.trimMargin()
        
        val dialog = AlertDialog.Builder(this)
            .setTitle("需要始终允许位置权限")
            .setMessage(message)
            .setPositiveButton("重新授权") { _, _ ->
                // 重新请求权限
                requestLocationPermissions()
            }
            .setNegativeButton("退出应用") { _, _ ->
                // 用户选择退出
                finish()
            }
            .setCancelable(false)
            .create()
        
        // 保存对话框引用
        activeDialog = dialog
        
        // 安全地显示对话框
        if (!isFinishing && !isDestroyed) {
            dialog.show()
        }
    }
    
    /**
     * 显示需要到设置中开启权限的对话框
     */
    private fun showSettingsDialog() {
        // 先关闭之前的对话框
        dismissActiveDialog()
        
        val message = """
            |维修工单应用需要位置权限才能正常工作。
            |
            |请在应用权限设置中：
            |1. 开启"位置信息"权限
            |2. 选择"始终允许"而不是"仅在使用应用时允许"
            |
            |为确保应用在后台稳定运行，建议您也设置设备的后台运行权限。
        """.trimMargin()
        
        val dialog = AlertDialog.Builder(this)
            .setTitle("需要位置权限")
            .setMessage(message)
            .setPositiveButton("前往设置") { _, _ ->
                // 打开应用设置页面
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                val uri = Uri.fromParts("package", packageName, null)
                intent.data = uri
                startActivityForResult(intent, SETTINGS_REQUEST_CODE)
            }
            .setNeutralButton("后台设置指南") { _, _ ->
                // 打开保活设置指南
                val intent = Intent(this, KeepAliveGuideActivity::class.java)
                startActivity(intent)
                // 稍后重新检查权限
                Handler(Looper.getMainLooper()).postDelayed({
                    checkPermissionsAndProceed()
                }, 500)
            }
            .setNegativeButton(R.string.exit) { _, _ ->
                // 用户选择退出应用
                finish()
            }
            .setCancelable(false)
            .create()
        
        // 保存对话框引用
        activeDialog = dialog
        
        // 安全地显示对话框
        if (!isFinishing && !isDestroyed) {
            dialog.show()
        }
    }
    
    /**
     * 权限请求结果回调
     */
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        
        if (requestCode == PERMISSION_REQUEST_CODE) {
            // 检查基本位置权限是否授予
            var allGranted = true
            if (grantResults.isNotEmpty()) {
                for (result in grantResults) {
                    if (result != PackageManager.PERMISSION_GRANTED) {
                        allGranted = false
                        break
                    }
                }
                
                if (allGranted) {
                    // 基本位置权限已授予，检查是否需要请求后台权限
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q &&
                        !PermissionUtils.hasBackgroundLocationPermission(this)) {
                        requestBackgroundLocationPermission()
                    } else {
                        // 所有权限都已授予，检查系统定位功能是否开启，并检查精确闹钟权限
                        ensureExactAlarmPermission()
                        checkLocationServiceAndProceed()
                    }
                } else {
                    // 权限被拒绝
                    permissionDeniedCount++
                    
                    if (permissionDeniedCount >= 2 || !shouldShowRequestPermissionRationale(Manifest.permission.ACCESS_FINE_LOCATION)) {
                        // 用户多次拒绝或选择了"不再询问"，显示引导去设置页面的对话框
                        showSettingsDialog()
                    } else {
                        // 第一次被拒绝，显示说明对话框
                        showPermissionExplanationDialog()
                    }
                }
            }
        } else if (requestCode == BACKGROUND_LOCATION_REQUEST_CODE) {
            // 处理后台位置权限请求结果
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // 后台位置权限已授予，继续检查系统定位功能（不显示提示，避免重复）
                ensureExactAlarmPermission()
                checkLocationServiceAndProceed()
            } else {
                // 后台位置权限被拒绝，但仍然可以继续使用应用（只是后台功能受限）
                ensureExactAlarmPermission()
                checkLocationServiceAndProceed()
            }
        }
    }
    
    /**
     * 设置页面返回结果回调
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (requestCode == SETTINGS_REQUEST_CODE || requestCode == PermissionUtils.SETTINGS_REQUEST_CODE) {
            // 从设置页面返回，重新检查权限和定位服务
            // 给用户一点时间看到返回界面，然后再检查
            Handler(Looper.getMainLooper()).postDelayed({
                checkPermissionsAndProceed()
            }, 500)
        }
    }
    
    /**
     * 使用 refreshToken 静默刷新 accessToken
     */
    private fun silentRefreshToken(refreshToken: String, callback: (Boolean) -> Unit) {
        lifecycleScope.launch {
            val success = withContext(Dispatchers.IO) {
                try {
                    val loginService = ApiClient.createService(com.example.repairorderapp.network.service.LoginService::class.java)
                    val response = loginService.refreshToken(mapOf("refreshToken" to refreshToken)).execute()
                    if (response.isSuccessful) {
                        val bodyStr = response.body()?.string() ?: ""
                        val json = JSONObject(bodyStr)
                        if (json.optInt("code", -1) == 200) {
                            val data = json.optJSONObject("data")
                            val newToken = data?.optString("token", "") ?: ""
                            val newRefreshToken = data?.optString("refreshToken", "") ?: ""
                            if (newToken.isNotEmpty() && newRefreshToken.isNotEmpty()) {
                                val tokenPrefs = getSharedPreferences("token_pref", Context.MODE_PRIVATE)
                                tokenPrefs.edit().apply {
                                    putString("accessToken", newToken)
                                    putString("refreshToken", newRefreshToken)
                                    // 不再设置过期时间，依赖服务器401响应判断过期
                                    commit()
                                }
                                return@withContext true
                            }
                        }
                    }
                    false
                } catch (e: Exception) {
                    Log.e(TAG, "silentRefreshToken error", e)
                    false
                }
            }
            callback(success)
        }
    }
    
    /**
     * Android 12(S)+ 精确闹钟权限检查与请求
     */
    private fun ensureExactAlarmPermission() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                val alarmManager = getSystemService(AlarmManager::class.java)
                if (alarmManager != null && !alarmManager.canScheduleExactAlarms()) {
                    val intent = Intent(Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM)
                    startActivity(intent)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查/请求精确闹钟权限失败", e)
        }
    }
} 