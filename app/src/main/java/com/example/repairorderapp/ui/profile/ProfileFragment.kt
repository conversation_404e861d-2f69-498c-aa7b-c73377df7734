package com.example.repairorderapp.ui.profile

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.GridLayout
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.example.repairorderapp.R
import com.example.repairorderapp.model.FunctionMenuItem
import com.example.repairorderapp.ui.login.LoginActivity
import com.example.repairorderapp.utils.PermissionManager
import com.example.repairorderapp.utils.MenuRenderer
import com.example.repairorderapp.utils.PermissionItem
import com.example.repairorderapp.utils.MenuItemWithPermission
import com.example.repairorderapp.util.SharedPrefsManager
import com.google.android.material.card.MaterialCardView
import com.example.repairorderapp.data.api.ApiClient
import com.example.repairorderapp.data.api.ApiResponse
import com.example.repairorderapp.data.api.WorkOrderApi
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.util.HashMap

class ProfileFragment : Fragment() {
    
    companion object {
        private const val TAG = "ProfileFragment"
    }
    
    private lateinit var permissionManager: PermissionManager
    private lateinit var menuRenderer: MenuRenderer
    private lateinit var functionsGrid: GridLayout
    private var loadingView: View? = null
    private var isInitialLoad = true
    
    // 工单数量
    private var pendingOrdersCount = 0
    private var myWorkOrderCount = 0
    private var appealOrderCount = 0

    // 防重复请求
    private var lastFetchTime = 0L
    private val FETCH_INTERVAL_MS = 5000L // 5秒内不重复请求
    
    // 功能按钮列表
    private lateinit var functionButtons: List<FunctionMenuItem>
    
    // API服务
    private lateinit var workOrderApi: WorkOrderApi
    
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_profile, container, false)
        
        try {
            // 初始化API服务
            workOrderApi = ApiClient.createService(WorkOrderApi::class.java)
            
            // 初始化权限管理器和菜单渲染器
            permissionManager = PermissionManager.getInstance(requireContext())
            menuRenderer = MenuRenderer.getInstance()
            
            // 获取功能按钮容器
            functionsGrid = view.findViewById(R.id.main_functions_grid)
            
            // 获取或创建加载视图
            loadingView = view.findViewById(R.id.functions_loading_view)
            if (loadingView == null) {
                // 如果布局中没有加载视图，查找父视图并添加一个
                val cardView = functionsGrid.parent as? ViewGroup
                if (cardView != null) {
                    loadingView = inflater.inflate(R.layout.loading_indicator, cardView, false)
                    cardView.addView(loadingView)
                    loadingView?.visibility = View.VISIBLE
                }
            } else {
                loadingView?.visibility = View.VISIBLE
            }
            
            // 在加载完成前隐藏功能网格
            functionsGrid.visibility = View.INVISIBLE
            
        } catch (e: Exception) {
            Log.e(TAG, "初始化视图时出错：${e.message}", e)
        }
        
        return view
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        try {
            // 注册权限变更监听
            permissionManager.registerPermissionObserver {
                updateFunctionButtons()
            }
            
            // 初始化功能按钮 - 检查权限是否已预加载
            if (permissionManager.isCacheInitialized()) {
                Log.d(TAG, "权限已预加载，直接初始化功能按钮")
                setupFunctionButtons()
            } else {
                Log.d(TAG, "权限未预加载，异步初始化权限后再加载功能按钮")
                permissionManager.initializePermissionsWithCallback {
                    setupFunctionButtons()
                }
            }
            
            // 设置用户基本信息
            setupUserInfo(view)
            
            // 为个人信息卡片添加点击事件
            view.findViewById<MaterialCardView>(R.id.profile_card)?.setOnClickListener {
                navigateToUserDetail()
            }
            
            // 设置设置按钮点击事件
            view.findViewById<View>(R.id.btn_settings)?.setOnClickListener {
                navigateToSettings()
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置视图事件时出错：${e.message}", e)
        }
    }
    
    override fun onResume() {
        super.onResume()
        // 每次页面恢复时获取工单数量，确保数据是最新的
        fetchWorkOrderCount()
    }
    
    override fun onDestroyView() {
        try {
            // 取消注册权限监听
            permissionManager.unregisterPermissionObserver {
                updateFunctionButtons()
            }
        } catch (e: Exception) {
            Log.e(TAG, "取消注册权限监听时出错：${e.message}", e)
        }
        
        super.onDestroyView()
    }
    
    /**
     * 获取工单数量统计
     */
    private fun fetchWorkOrderCount() {
        try {
            // 防重复请求检查
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastFetchTime < FETCH_INTERVAL_MS) {
                Log.d(TAG, "跳过重复的工单数量请求，距离上次请求仅${currentTime - lastFetchTime}ms")
                return
            }
            lastFetchTime = currentTime

            // 获取token
            val token = SharedPrefsManager(requireContext()).getAuthToken()
            if (token.isEmpty()) {
                Log.e(TAG, "获取工单数量失败：token为空")
                return
            }
            
            // 调用API获取工单数量 - 现在自动具有全局异常处理
            workOrderApi.getWorkOrderCount(token).enqueue(object : Callback<ApiResponse<Map<String, Any>>> {
                override fun onResponse(call: Call<ApiResponse<Map<String, Any>>>, response: Response<ApiResponse<Map<String, Any>>>) {
                    if (response.isSuccessful && response.body()?.code == 200) {
                        val data = response.body()?.data
                        if (data != null) {
                            // 解析数据
                            pendingOrdersCount = (data["pendingOrdersCount"] as? String)?.toIntOrNull() ?: 0
                            myWorkOrderCount = (data["myWorkOrderCount"] as? String)?.toIntOrNull() ?: 0
                            appealOrderCount = (data["appealOrderCount"] as? String)?.toIntOrNull() ?: 0

                            Log.d(TAG, "获取工单数量成功：待接工单=$pendingOrdersCount, 我的工单=$myWorkOrderCount, 申诉单=$appealOrderCount")

                            // 更新UI
                            if (isAdded) {
                                updateFunctionButtonBadges()
                            }
                        }
                    } else {
                        Log.e(TAG, "获取工单数量失败：${response.code()}")
                    }
                }

                override fun onFailure(call: Call<ApiResponse<Map<String, Any>>>, t: Throwable) {
                    Log.e(TAG, "获取工单数量网络请求失败：${t.message}", t)
                    // 异常会被全局代理自动处理，这里只需要记录日志
                }
            })
        } catch (e: Exception) {
            Log.e(TAG, "获取工单数量时出错：${e.message}", e)
        }
    }
    
    /**
     * 更新功能按钮上的角标
     */
    private fun updateFunctionButtonBadges() {
        try {
            if (!::functionButtons.isInitialized || !isAdded) {
                return
            }
            
            // 遍历功能网格中的所有按钮视图
            for (i in 0 until functionsGrid.childCount) {
                val buttonView = functionsGrid.getChildAt(i)
                val badgeView = buttonView.findViewById<TextView>(R.id.badge_count)
                val functionTitle = buttonView.findViewById<TextView>(R.id.text_function_name)?.text.toString()
                
                // 根据功能名称设置对应的角标
                when (functionTitle) {
                    "待接工单" -> {
                        updateBadge(badgeView, pendingOrdersCount)
                    }
                    "我的工单" -> {
                        updateBadge(badgeView, myWorkOrderCount)
                    }
                    "申诉单" -> {
                        updateBadge(badgeView, appealOrderCount)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新角标时出错：${e.message}", e)
        }
    }
    
    /**
     * 更新单个角标
     */
    private fun updateBadge(badgeView: TextView?, count: Int) {
        badgeView?.let {
            if (count > 0) {
                it.visibility = View.VISIBLE
                it.text = if (count > 99) "99+" else count.toString()
            } else {
                it.visibility = View.GONE
            }
        }
    }
    
    private fun setupUserInfo(view: View) {
        try {
            // 从SharedPreferences获取用户信息
            val prefs = requireActivity().getSharedPreferences("token_pref", Context.MODE_PRIVATE)
            val userName = prefs.getString("userName", "未知用户") ?: "未知用户"
            val userCode = prefs.getString("userCode", "") ?: ""
            
            // 更新UI显示
            view.findViewById<TextView>(R.id.text_user_name)?.text = userName
            view.findViewById<TextView>(R.id.text_user_id)?.text = "工号：$userCode"
        } catch (e: Exception) {
            Log.e(TAG, "设置用户信息时出错：${e.message}", e)
        }
    }
    
    private fun setupFunctionButtons() {
        try {
            // 定义功能按钮列表（包含权限信息）
            functionButtons = listOf(
                FunctionMenuItem(
                    1,
                    "工单管理",
                    R.drawable.ic_work_order,
                    { navigateToOrderManagement() },
                    "/workPool"
                ),
                FunctionMenuItem(
                    2,
                    "工程师管理",
                    R.drawable.ic_user,
                    { navigateToEngineerManagement() },
                    "/engineerManagement"
                ),
                FunctionMenuItem(
                    3,
                    "位置地图",
                    R.drawable.ic_map,
                    { navigateToMap() },
                    "/map"
                ),
                FunctionMenuItem(
                    4,
                    "工程师仓",
                    R.drawable.shelf_position_24dp,
                    { navigateToEngineerWarehouse() },
                    "/engineerWarehouse"
                ),
                FunctionMenuItem(
                    5,
                    "客户仓库",
                    R.drawable.inventory_24dp,
                    { navigateToCustomerWarehouse() },
                    "/customerWarehouse"
                ),
                FunctionMenuItem(
                    6,
                    "客户管理",
                    R.drawable.ic_people_24dp,
                    { navigateToCustomerManagement() },
                    "/customer"
                ),
                FunctionMenuItem(
                    7,
                    "时效统计",
                    R.drawable.ic_statistics,
                    { navigateToStatistics() },
                    "/statistics"
                ),
                FunctionMenuItem(
                    7,
                    "待接工单",
                    R.drawable.ic_orders,
                    { navigateToPendingOrders() },
                    "/pendingOrder"
                ),
                FunctionMenuItem(
                    8,
                    "我的工单",
                    R.drawable.ic_clock,
                    { navigateToMyOrders() },
                    "/myWorkOrder"
                ),
                FunctionMenuItem(
                    9,
                    "申诉单",
                    R.drawable.ic_edit,
                    { navigateToAppealOrders() },
                    "/appealOrder"
                ),
                FunctionMenuItem(
                    10,
                    "个人仓库",
                    R.drawable.ic_info,
                    { navigateToPersonalWarehouse() },
                    "/wareStore"
                ),
                FunctionMenuItem(
                    11,
                    "耗材仓库",
                    R.drawable.warehouse_24dp,
                    { navigateToConsumableWarehouse() },
                    "/warehouse"
                ),

                FunctionMenuItem(
                    12,
                    "申请领料",
                    R.drawable.package_24dp,
                    { navigateToWareApply() },
                    "/wareApply"
                ),
                FunctionMenuItem(
                    13,
                    "申请退料",
                    R.drawable.remove_shopping_cart_24dp,
                    { navigateToReturnApply() },
                    "/returnApply"
                ),
                FunctionMenuItem(
                    14,
                    "服务评价",
                    R.drawable.ic_star_filled,
                    { navigateToEvaluationList() },
                    "/evaluation"
                ),
                FunctionMenuItem(
                    15,
                    "知识库",
                    R.drawable.ic_learn,
                    { navigateToLearn() },
                    "/engLearn"
                )
            )
            
            // 优化后的更新功能按钮方法 - 使用同步方式过滤权限
            updateFunctionButtonsOptimized(functionButtons)

            // 注意：工单数量的获取由 onResume() 负责，避免重复调用
        } catch (e: Exception) {
            Log.e(TAG, "设置功能按钮时出错：${e.message}", e)
            // 确保加载出错时也隐藏加载视图
            showFunctionGrid()
        }
    }
    
    private fun updateFunctionButtonsOptimized(functionButtons: List<FunctionMenuItem>) {
        try {
            // 直接同步过滤功能按钮
            val permissionCodes = permissionManager.getPermissions()
            
            // 手动过滤功能按钮
            val visibleButtons = functionButtons.filter { functionButton ->
                functionButton.permission.isEmpty() || permissionCodes.contains(functionButton.permission)
            }
            
            // 更新UI
            updateFunctionButtonsUI(visibleButtons)
        } catch (e: Exception) {
            Log.e(TAG, "异步更新功能按钮时出错：${e.message}", e)
            // 确保加载出错时也隐藏加载视图
            showFunctionGrid()
        }
    }
    
    private fun updateFunctionButtonsAsync(functionButtons: List<FunctionMenuItem>) {
        try {
            // 后备方案：使用异步渲染
            menuRenderer.renderMenuAsync(permissionManager.getPermissions().map { code ->
                PermissionItem(
                    id = code,
                    code = code,
                    name = code,
                    type = "menu"
                )
            }) { menuItems ->
                try {
                    // 安全地过滤功能按钮
                    // 首先获取所有有权限的菜单项的权限编码
                    val permissionCodes = permissionManager.getPermissions()
                    
                    // 手动过滤功能按钮，而不是依赖permissionManager.filterMenuItems
                    val visibleButtons = functionButtons.filter { functionButton ->
                        functionButton.permission.isEmpty() || permissionCodes.contains(functionButton.permission)
                    }
                    
                    // 更新UI
                    updateFunctionButtonsUI(visibleButtons)
                } catch (e: Exception) {
                    // 防止UI崩溃
                    Log.e(TAG, "过滤菜单项时出错: ${e.message}", e)
                    // 确保加载出错时也隐藏加载视图
                    showFunctionGrid()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "异步更新功能按钮时出错：${e.message}", e)
            // 确保加载出错时也隐藏加载视图
            showFunctionGrid()
        }
    }
    
    private fun updateFunctionButtonsUI(buttons: List<FunctionMenuItem>) {
        try {
            // 确保UI操作在主线程
            if (isAdded && functionsGrid != null) {
                functionsGrid.removeAllViews()
                
                buttons.forEach { button ->
                    try {
                        val buttonView = layoutInflater.inflate(R.layout.item_function_grid, functionsGrid, false)
                        
                        // 设置按钮图标
                        buttonView.findViewById<ImageView>(R.id.icon_function)?.setImageResource(button.icon)
                        
                        // 设置按钮文本
                        buttonView.findViewById<TextView>(R.id.text_function_name)?.text = button.title
                        
                        // 设置点击事件
                        buttonView.setOnClickListener {
                            try {
                                button.action.invoke()
                            } catch (e: Exception) {
                                Log.e(TAG, "按钮点击事件处理错误：${e.message}", e)
                                Toast.makeText(requireContext(), "操作失败，请重试", Toast.LENGTH_SHORT).show()
                            }
                        }
                        
                        functionsGrid.addView(buttonView)
                    } catch (e: Exception) {
                        Log.e(TAG, "创建功能按钮视图时出错：${e.message}", e)
                    }
                }
                
                // 现在显示功能网格，隐藏加载视图
                showFunctionGrid()
                
                // 更新角标
                updateFunctionButtonBadges()
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新UI时出错：${e.message}", e)
            // 确保加载出错时也隐藏加载视图
            showFunctionGrid()
        }
    }
    
    private fun showFunctionGrid() {
        try {
            if (isAdded) {
                // 隐藏加载视图
                loadingView?.visibility = View.GONE
                
                // 显示功能网格，使用淡入动画
                functionsGrid.apply {
                    alpha = 0f
                    visibility = View.VISIBLE
                    animate()
                        .alpha(1f)
                        .setDuration(200)
                        .start()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "显示功能网格时出错：${e.message}", e)
            // 出错时直接显示
            functionsGrid.visibility = View.VISIBLE
            loadingView?.visibility = View.GONE
        }
    }
    
    private fun updateFunctionButtons() {
        try {
            if (!isInitialLoad) {
                // 这是一次更新操作，需要显示加载状态
                loadingView?.visibility = View.VISIBLE
                functionsGrid.visibility = View.INVISIBLE
            }
            setupFunctionButtons()
            isInitialLoad = false
        } catch (e: Exception) {
            Log.e(TAG, "更新功能按钮时出错：${e.message}", e)
            // 确保加载出错时也隐藏加载视图
            showFunctionGrid()
        }
    }
    
    // 导航方法，添加安全处理
    private fun navigateToOrderManagement() {
        try {
            findNavController().navigate(R.id.repairOrdersFragment)
        } catch (e: Exception) {
            Log.e(TAG, "导航到工单管理失败：${e.message}", e)
            showNavigationError()
        }
    }
    
    private fun navigateToEngineerManagement() {
        try {
            findNavController().navigate(R.id.action_profileFragment_to_engineerManagementFragment)
        } catch (e: Exception) {
            Log.e(TAG, "导航到工程师管理失败：${e.message}", e)
            showNavigationError()
        }
    }
    
    private fun navigateToMap() {
        try {
            val navController = findNavController()
            // 仅当当前目的地不是 MapFragment 时才执行导航
            if (navController.currentDestination?.id == R.id.profileFragment) {
                navController.navigate(R.id.action_profileFragment_to_mapFragment)
            }
        } catch (e: Exception) {
            Log.e(TAG, "导航到地图失败：${e.message}", e)
            showNavigationError()
        }
    }
    
    private fun navigateToPendingOrders() {
        try {
            // 创建Bundle传递参数
            val bundle = Bundle().apply {
                putString("type", "pending") // 添加标识，表明是"待接工单"
            }
            
            // 导航并传递参数
            findNavController().navigate(R.id.orderListFragment, bundle)
        } catch (e: Exception) {
            Log.e(TAG, "导航到待接工单失败：${e.message}", e)
            showNavigationError()
        }
    }
    
    private fun navigateToMyOrders() {
        try {
            // 获取当前工程师ID
            val engineerId = SharedPrefsManager(requireContext()).getEngineerId()
            val userName = SharedPrefsManager(requireContext()).getUserName()
            
            // 创建Bundle传递参数
            val bundle = Bundle().apply {
                putString("engineerId", engineerId)
                putString("engineerName", userName)
                putString("type", "my") // 添加标识，表明是"我的工单"
            }
            
            // 导航并传递参数
            findNavController().navigate(R.id.orderListFragment, bundle)
        } catch (e: Exception) {
            Log.e(TAG, "导航到我的工单失败：${e.message}", e)
            showNavigationError()
        }
    }
    
    private fun navigateToAppealOrders() {
        try {
            // 创建Bundle传递参数
            val bundle = Bundle().apply {
                putString("type", "appeal") // 添加标识，表明是"申诉单"
            }
            
            // 导航并传递参数
            findNavController().navigate(R.id.orderListFragment, bundle)
        } catch (e: Exception) {
            Log.e(TAG, "导航到申诉单失败：${e.message}", e)
            showNavigationError()
        }
    }
    
    private fun navigateToPersonalWarehouse() {
        try {
            findNavController().navigate(R.id.warehouseFragment)
        } catch (e: Exception) {
            Log.e(TAG, "导航到个人仓库失败：${e.message}", e)
            showNavigationError()
        }
    }
    
    private fun navigateToConsumableWarehouse() {
        try {
            findNavController().navigate(R.id.consumableWarehouseFragment)
        } catch (e: Exception) {
            Log.e(TAG, "导航到耗材仓库失败：${e.message}", e)
            showNavigationError()
        }
    }
    
    /**
     * 导航到客户管理页面
     */
    private fun navigateToCustomerManagement() {
        try {
            findNavController().navigate(R.id.action_profileFragment_to_customerFragment)
        } catch (e: Exception) {
            Log.e(TAG, "导航到客户管理页面失败：${e.message}", e)
            showNavigationError()
        }
    }
    
    private fun navigateToUserDetail() {
        try {
            // 获取当前用户/工程师ID
            val engineerId = SharedPrefsManager(requireContext()).getEngineerId()
            
            if (engineerId.isNotEmpty()) {
                // 创建Bundle并设置工程师ID
                val bundle = Bundle().apply {
                    putString("engineerId", engineerId)
                }
                
                // 导航到工程师详情页面
                findNavController().navigate(R.id.action_profileFragment_to_engineerDetailFragment, bundle)
            } else {
                Toast.makeText(requireContext(), "未找到工程师信息", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "导航到用户详情页面失败：${e.message}", e)
            showNavigationError()
        }
    }
    /**
     * 导航到申领耗材页面
     */
    private fun navigateToWareApply() {
        try {
            findNavController().navigate(R.id.action_profileFragment_to_warehouseApplyFragment)
        } catch (e: Exception) {
            Log.e(TAG, "导航到申领耗材页面失败：${e.message}", e)
            showNavigationError()
        }
    }

    /**
     * 导航到申请退料页面
     */
    private fun navigateToReturnApply() {
        try {
            findNavController().navigate(R.id.action_profileFragment_to_warehouseReturnApplyFragment)
        } catch (e: Exception) {
            Log.e(TAG, "导航到申请退料页面失败：${e.message}", e)
            showNavigationError()
        }
    }

    /**
     * 导航到系统设置页面
     */
    private fun navigateToSettings() {
        try {
            findNavController().navigate(R.id.action_profileFragment_to_settingsFragment)
        } catch (e: Exception) {
            Log.e(TAG, "导航到系统设置页面失败：${e.message}", e)
            showNavigationError()
        }
    }

    /**
     * 导航到工程师仓页面
     */
    private fun navigateToEngineerWarehouse() {
        try {
            findNavController().navigate(R.id.action_profileFragment_to_engineerWarehouseFragment)
        } catch (e: Exception) {
            Log.e(TAG, "导航到工程师仓页面失败：${e.message}", e)
            showNavigationError()
        }
    }

    /**
     * 导航到客户仓库页面
     */
    private fun navigateToCustomerWarehouse() {
        try {
            findNavController().navigate(R.id.customerWarehouseFragment)
        } catch (e: Exception) {
            Log.e(TAG, "导航到客户仓库页面失败：${e.message}", e)
            showNavigationError()
        }
    }

    /**
     * 导航到服务评价页面
     */
    private fun navigateToEvaluationList() {
        try {
            findNavController().navigate(R.id.action_profileFragment_to_evaluationListFragment)
        } catch (e: Exception) {
            Log.e(TAG, "导航到服务评价页面失败：${e.message}", e)
            showNavigationError()
        }
    }

    /**
     * 导航到知识库页面
     */
    private fun navigateToLearn() {
        try {
            findNavController().navigate(R.id.learnFragment)
        } catch (e: Exception) {
            Log.e(TAG, "导航到知识库页面失败：${e.message}", e)
            showNavigationError()
        }
    }

    /**
     * 导航到时效统计页面
     */
    private fun navigateToStatistics() {
        try {
            findNavController().navigate(R.id.action_profileFragment_to_statisticsFragment)
        } catch (e: Exception) {
            Log.e(TAG, "导航到时效统计页面失败：${e.message}", e)
            showNavigationError()
        }
    }

    private fun showNavigationError() {
        try {
            if (isAdded) {
                Toast.makeText(requireContext(), "页面跳转失败，请重试", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "显示导航错误提示失败：${e.message}", e)
        }
    }
} 