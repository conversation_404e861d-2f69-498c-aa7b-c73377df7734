package com.example.repairorderapp.ui.orders

import android.app.AlertDialog
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.Toast
import android.widget.TextView
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.example.repairorderapp.R
import com.example.repairorderapp.data.api.ApiClient
import com.example.repairorderapp.data.api.ApiResponse
import com.example.repairorderapp.data.api.WorkOrderApi
import com.example.repairorderapp.model.RepairOrder
import com.example.repairorderapp.model.Engineer
import com.google.android.material.chip.Chip
import com.google.android.material.chip.ChipGroup
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.util.*
import java.text.SimpleDateFormat
import com.example.repairorderapp.ui.orders.WorkOrderResponse
import com.example.repairorderapp.ui.orders.WorkOrderData
import com.example.repairorderapp.ui.orders.WorkOrderItem
import com.example.repairorderapp.ui.orders.Customer
import com.example.repairorderapp.ui.orders.EngineerInfo
import com.example.repairorderapp.ui.orders.DictItem
import com.google.gson.JsonObject
import com.google.gson.JsonArray
import androidx.lifecycle.ViewModelProvider
import kotlinx.coroutines.*
import androidx.lifecycle.lifecycleScope
import com.example.repairorderapp.model.EngineerWorkData
import android.util.TypedValue

class RepairOrdersFragment : Fragment() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var swipeRefreshLayout: SwipeRefreshLayout
    private lateinit var emptyView: View
    private lateinit var chipGroup: ChipGroup
    private lateinit var searchEdit: EditText
    private lateinit var adapter: RepairOrderAdapter

    private lateinit var viewModel: RepairOrdersViewModel

    private var currentTab = 0 // 0: 全部, 1: 待接工单, 2: 指派给我的, 3: 我的工单
    private var currentPage = 1
    private var totalItems = 0
    private var isLoading = false
    private var isRestoringState = false
    private val pageSize = 50
    private var currentWorkOrderId: String? = null
    private var filterEngineerId: String? = null

    // 判断是否从工单管理菜单进入
    private var isFromWorkOrderMenu = true

    // 保存RecyclerView的滚动位置
    private var listPosition = 0
    private var listOffset = 0

    private val workOrderApi = ApiClient.createService(WorkOrderApi::class.java)

    private var isTabsInitialized = false // 只初始化一次Tabs
    private var isUserAction = false // 只响应用户主动操作

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_repair_orders, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel = ViewModelProvider(requireActivity())[RepairOrdersViewModel::class.java]
        Log.d("RepairOrders", "onViewCreated: fragment=" + this.hashCode() + ", isDataLoaded=" + viewModel.isDataLoaded)

        // 判断是否从工单管理菜单进入
        // 通过判断导航路径和当前Fragment的ID来确定
        val currentDestination = findNavController().currentDestination
        isFromWorkOrderMenu = (currentDestination?.id == R.id.repairOrdersFragment)
        Log.d("RepairOrders", "是否从工单管理菜单进入: $isFromWorkOrderMenu")

        // 初始化视图
        recyclerView = view.findViewById(R.id.recycler_orders)
        swipeRefreshLayout = view.findViewById(R.id.swipe_refresh)
        emptyView = view.findViewById(R.id.empty_view)
        chipGroup = view.findViewById(R.id.chip_group)
        searchEdit = view.findViewById(R.id.edit_search)

        // 获取其他UI元素
        val searchContainer = view.findViewById<View>(R.id.search_container)
        val btnSearch = view.findViewById<View>(R.id.btn_search)
        val btnClearSearch = view.findViewById<View>(R.id.btn_clear_search)
        val tvCurrentFilter = view.findViewById<TextView>(R.id.tv_current_filter)

        // 1. 先恢复UI状态，暂不注册监听器
        isRestoringState = true
        isRestoringState = false

        // 只在首次进入时初始化Tabs，避免重建触发监听器
        if (!isTabsInitialized) {
            initTabs()
            isTabsInitialized = true
        }

        // 搜索按钮点击事件
        btnSearch.setOnClickListener {
            // 切换搜索框可见性
            searchContainer.visibility = if (searchContainer.visibility == View.VISIBLE) View.GONE else View.VISIBLE

            // 如果搜索框显示，则设置焦点并显示键盘
            if (searchContainer.visibility == View.VISIBLE) {
                searchEdit.requestFocus()
                val imm = requireContext().getSystemService(android.content.Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
                imm.showSoftInput(searchEdit, android.view.inputmethod.InputMethodManager.SHOW_IMPLICIT)
            }
        }

        // 清除搜索按钮点击事件
        btnClearSearch.setOnClickListener {
            isUserAction = true
            searchEdit.setText("")
            btnClearSearch.visibility = View.GONE
            // 隐藏键盘
            val imm = requireContext().getSystemService(android.content.Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
            imm.hideSoftInputFromWindow(searchEdit.windowToken, 0)
            isUserAction = false
        }

        // 确保ChipGroup包含与Tab对应的Chip
        initTabs()

        // 初始显示全部工单
        view.findViewById<TextView>(R.id.tv_current_filter)?.text = "当前：全部工单"

        // 设置线性布局，一行一个工单
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        // 创建并设置适配器
        adapter = RepairOrderAdapter(
            requireContext(),
            emptyList(),
            // 卡片点击 - 始终导航到详情
            onItemClick = { order ->
                // 对于所有工单，导航到详情页面
                val bundle = Bundle().apply {
                    putString("orderId", order.id)
                }
                findNavController().navigate(R.id.action_to_order_detail, bundle)
            },
            // 按钮点击 - 根据状态执行不同操作
            onButtonClick = { order ->
                if (order.status.contains("待") && order.engineerId == null) {
                    // 对于待分配的工单，显示分配工程师的对话框
                    val productId = order.productId
                    if (productId.isNullOrBlank()) {
                        Toast.makeText(requireContext(), "工单缺少产品ID，无法分配工程师", Toast.LENGTH_SHORT).show()
                        return@RepairOrderAdapter
                    }
                    Log.d("RepairOrders", "点击分配工单按钮: 工单号=${order.code}, ID=${order.id}, 产品ID: $productId")
                    showAssignEngineerDialog(order.id, productId)
                } else {
                    // 对于其他工单，导航到详情页面
                    val bundle = Bundle().apply {
                        putString("orderId", order.id)
                    }
                    findNavController().navigate(R.id.action_to_order_detail, bundle)
                }
            }
        )
        recyclerView.adapter = adapter

        // 下拉刷新（只在用户主动操作时重置 isDataLoaded）
        swipeRefreshLayout.setOnRefreshListener {
            Log.d("RepairOrders", "用户下拉刷新，重置 isDataLoaded=false")
            viewModel.currentPage = 1
            viewModel.totalItems = 0
            totalItems = 0
            viewModel.isDataLoaded = false
            loadWorkOrders()
        }

        // 2. 恢复完毕后再注册监听器
        searchEdit.addTextChangedListener {
            if (isRestoringState) return@addTextChangedListener
            if (!isUserAction && searchEdit.text.isEmpty()) return@addTextChangedListener // 只响应用户主动清空
            btnClearSearch.visibility = if (searchEdit.text.isNotEmpty()) View.VISIBLE else View.GONE
            if (searchEdit.text.isEmpty()) {
                Log.d("RepairOrders", "搜索内容清空，重置 isDataLoaded=false")
                viewModel.currentPage = 1
                viewModel.totalItems = 0
                totalItems = 0
                viewModel.allOrders.value = emptyList()
                viewModel.filteredOrders.value = emptyList()
                viewModel.isDataLoaded = false
                loadWorkOrders()
            } else {
                filterOrders()
            }
        }

        // 监听搜索框回车键
        searchEdit.setOnEditorActionListener { _, actionId, event ->
            if (actionId == android.view.inputmethod.EditorInfo.IME_ACTION_SEARCH ||
                (event?.keyCode == android.view.KeyEvent.KEYCODE_ENTER && event.action == android.view.KeyEvent.ACTION_DOWN)) {
                // 隐藏键盘
                val imm = requireContext().getSystemService(android.content.Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
                imm.hideSoftInputFromWindow(searchEdit.windowToken, 0)

                // 隐藏搜索框
                searchContainer.visibility = View.GONE

                return@setOnEditorActionListener true
            }
            false
        }

        // 状态筛选
        chipGroup.setOnCheckedChangeListener { _, checkedId ->
            if (isRestoringState) return@setOnCheckedChangeListener
            if (checkedId != R.id.chip_engineer_orders) {
                filterEngineerId = null
                view.findViewById<Chip>(R.id.chip_engineer_orders)?.text = "工程师工单"
            }
            val filterText = when (checkedId) {
                R.id.chip_uncompleted -> "未完成"
                R.id.chip_pending -> "待接单"
                R.id.chip_processing -> "处理中"
                R.id.chip_waiting_confirm -> "待确认/结算"
                R.id.chip_engineer_orders -> if (filterEngineerId != null) "工程师：${view.findViewById<Chip>(R.id.chip_engineer_orders)?.text}" else "工程师工单"
                else -> "全部工单"
            }
            tvCurrentFilter.text = "当前：$filterText (加载中...)"
            when (checkedId) {
                R.id.chip_uncompleted -> currentTab = 0
                R.id.chip_pending -> currentTab = 1
                R.id.chip_processing -> currentTab = 0
                R.id.chip_waiting_confirm -> currentTab = 0
                R.id.chip_engineer_orders -> {
                    val tokenPrefs = requireContext().getSharedPreferences("token_pref", android.content.Context.MODE_PRIVATE)
                    val engineerId = tokenPrefs.getString("userId", "")
                    if (!engineerId.isNullOrEmpty()) {
                        showEngineerSelectionForFiltering(engineerId)
                    } else {
                        workOrderApi.getEngineerList("").enqueue(object : Callback<EngineerListResponse> {
                            override fun onResponse(call: Call<EngineerListResponse>, response: Response<EngineerListResponse>) {
                                if (response.isSuccessful && response.body()?.code == 200) {
                                    val engineers = response.body()?.getEngineerList() ?: emptyList()
                                    if (engineers.isNotEmpty()) {
                                        showEngineerSelectionForFiltering()
                                    } else {
                                        Toast.makeText(requireContext(), "没有可用的工程师", Toast.LENGTH_SHORT).show()
                                        chipGroup.check(R.id.chip_uncompleted)
                                        tvCurrentFilter.text = "当前：未完成"
                                    }
                                } else {
                                    Toast.makeText(requireContext(), "获取工程师列表失败", Toast.LENGTH_SHORT).show()
                                    chipGroup.check(R.id.chip_uncompleted)
                                    tvCurrentFilter.text = "当前：未完成"
                                }
                            }
                            override fun onFailure(call: Call<EngineerListResponse>, t: Throwable) {
                                Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
                                chipGroup.check(R.id.chip_uncompleted)
                                tvCurrentFilter.text = "当前：未完成"
                            }
                        })
                    }
                    currentTab = 0
                }
                else -> currentTab = 0
            }
            val hasSearchText = searchEdit.text.isNotEmpty()
            if (hasSearchText && viewModel.allOrders.value?.isNotEmpty() == true && checkedId != R.id.chip_engineer_orders) {
                Log.d("RepairOrders", "有搜索内容，直接应用本地筛选而不重新加载数据")
                applyFilters()
            } else {
                Log.d("RepairOrders", "用户切换筛选，重置 isDataLoaded=false")
                viewModel.currentPage = 1
                viewModel.totalItems = 0
                totalItems = 0
                viewModel.allOrders.value = emptyList()
                viewModel.filteredOrders.value = emptyList()
                viewModel.isDataLoaded = false
                loadWorkOrders()
            }
        }

        // 设置滚动监听器用于分页加载
        recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                val visibleItemCount = layoutManager.childCount
                val totalItemCount = layoutManager.itemCount
                val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()

                // 判断是否需要加载更多：已滚动到底部且还有更多数据
                // 注意：只有在没有搜索内容的情况下才加载更多，因为搜索是在本地数据上进行的
                val hasSearchText = searchEdit.text.isNotEmpty()
                if (!isLoading && !hasSearchText && (visibleItemCount + firstVisibleItemPosition) >= totalItemCount
                    && firstVisibleItemPosition >= 0 && viewModel.allOrders.value?.size ?: 0 < viewModel.totalItems) {
                    Log.d("RepairOrders", "触发加载更多: 当前已加载${viewModel.allOrders.value?.size ?: 0}条，总共${viewModel.totalItems}条")
                    loadMoreOrders()
                }

                // 向下滚动时隐藏面板
                if (dy > 10) {
                    searchContainer.visibility = View.GONE
                }
            }
        })

        // 监听数据变化，自动刷新UI
        viewModel.filteredOrders.observe(viewLifecycleOwner) { orders ->
            adapter.updateData(orders)
            restoreScrollPosition()
        }

        if (!viewModel.isDataLoaded) {
            Log.d("RepairOrders", "首次进入页面，加载数据")
            loadWorkOrders()
        } else {
            Log.d("RepairOrders", "使用现有数据，不重新加载")
            // 恢复totalItems状态
            totalItems = viewModel.totalItems
            Log.d("RepairOrders", "恢复状态: 总工单数=${viewModel.totalItems}, 已加载${viewModel.allOrders.value?.size ?: 0}条")
            adapter.updateData(viewModel.filteredOrders.value ?: emptyList())

            // 确保在使用现有数据时也更新筛选文本，显示正确的筛选状态
            val selectedChipId = chipGroup.checkedChipId
            val filterText = when (selectedChipId) {
                R.id.chip_uncompleted -> "未完成"
                R.id.chip_pending -> "待接单"
                R.id.chip_processing -> "处理中"
                R.id.chip_waiting_confirm -> "待确认/结算"
                R.id.chip_engineer_orders -> {
                    if (filterEngineerId != null) "工程师：${view?.findViewById<Chip>(R.id.chip_engineer_orders)?.text}" else "工程师工单"
                }
                View.NO_ID -> "全部工单" // 没有选中任何筛选项时显示"全部工单"
                else -> "全部工单"
            }
            view?.findViewById<TextView>(R.id.tv_current_filter)?.text = "当前：$filterText (${viewModel.filteredOrders.value?.size ?: 0}条工单)"

            restoreScrollPosition()
        }
    }

    override fun onPause() {
        super.onPause()
        val layoutManager = recyclerView.layoutManager as? LinearLayoutManager ?: return
        listPosition = layoutManager.findFirstVisibleItemPosition()
        val firstVisibleView = layoutManager.findViewByPosition(listPosition)
        listOffset = firstVisibleView?.top ?: 0
        Log.d("RepairOrders", "保存列表位置: position=$listPosition, offset=$listOffset")
    }

    /**
     * 初始化工单选项卡
     */
    private fun initTabs() {
        // 确保ChipGroup已清空
        chipGroup.removeAllViews()

        // 创建"未完成"选项卡
        val uncompletedChip = Chip(requireContext()).apply {
            id = R.id.chip_uncompleted
            text = "未完成"
            isCheckable = true
            // 从工单管理菜单进入时，默认不选中任何筛选项
            isChecked = !isFromWorkOrderMenu
            setChipStyle(this)
        }

        // 创建"待接工单"选项卡
        val pendingChip = Chip(requireContext()).apply {
            id = R.id.chip_pending
            text = "待接单"
            isCheckable = true
            isChecked = false
            setChipStyle(this)
        }

        // 创建"处理中"选项卡
        val processingChip = Chip(requireContext()).apply {
            id = R.id.chip_processing
            text = "处理中"
            isCheckable = true
            isChecked = false
            setChipStyle(this)
        }

        // 创建"待确认/结算"选项卡
        val waitingConfirmChip = Chip(requireContext()).apply {
            id = R.id.chip_waiting_confirm
            text = "待确认/结算"
            isCheckable = true
            isChecked = false
            setChipStyle(this)
        }

        // 添加到ChipGroup
        chipGroup.addView(uncompletedChip)
        chipGroup.addView(pendingChip)
        chipGroup.addView(processingChip)
        chipGroup.addView(waitingConfirmChip)

        // 只有不是从工单管理菜单进入时，才显示工程师工单筛选按钮
        if (!isFromWorkOrderMenu) {
            // 创建"工程师工单"选项卡
            val engineerOrdersChip = Chip(requireContext()).apply {
                id = R.id.chip_engineer_orders
                text = "工程师工单"
                isCheckable = true
                isChecked = false
                setChipStyle(this)
            }
            chipGroup.addView(engineerOrdersChip)
        }

        // 从工单管理菜单进入时，默认不选中任何筛选项
        if (isFromWorkOrderMenu) {
            chipGroup.clearCheck()
        }
    }

    /**
     * 设置Chip样式为水平筛选样式
     */
    private fun setChipStyle(chip: Chip) {
        chip.apply {
            setTextSize(TypedValue.COMPLEX_UNIT_SP, 13f)
            chipMinHeight = resources.getDimension(R.dimen.chip_min_height)
            chipBackgroundColor = resources.getColorStateList(R.color.chip_blue_background_color, null)
            setTextColor(resources.getColorStateList(R.color.chip_text_color, null))
            chipStrokeWidth = resources.getDimension(R.dimen.chip_stroke_width)
            chipStrokeColor = resources.getColorStateList(R.color.divider, null)
            isCheckedIconVisible = false
            chipStartPadding = resources.getDimension(R.dimen.chip_padding)
            chipEndPadding = resources.getDimension(R.dimen.chip_padding)
        }
    }

    private fun loadWorkOrders() {
        if (isLoading) {
            Log.d("RepairOrders", "loadWorkOrders: 已在加载中，跳过本次调用")
            return
        }
        Log.d("RepairOrders", "loadWorkOrders: fragment=" + this.hashCode() + ", isDataLoaded=" + viewModel.isDataLoaded)
        isLoading = true
        swipeRefreshLayout.isRefreshing = true
        emptyView.visibility = View.GONE
        viewModel.currentPage = 1

        // 构建请求参数，使用JsonObject支持数组类型参数
        val jsonObject = JsonObject().apply {
            addProperty("pageNumber", viewModel.currentPage.toString())
            addProperty("pageSize", pageSize.toString())
            // 获取用户ID
            val tokenPrefs = requireContext().getSharedPreferences("token_pref", android.content.Context.MODE_PRIVATE)
            val userId = tokenPrefs.getString("userId", "") ?: ""
            // 工程师筛选
            if (filterEngineerId != null) {
                addProperty("engineerId", filterEngineerId)
            } else if (currentTab == 2 || currentTab == 3) {
                if (userId.isNotEmpty()) {
                    addProperty("engineerId", userId)
                }
            }
            // 状态筛选
            val selectedChipId = chipGroup.checkedChipId
            val statusArray = JsonArray()

            // 处理没有选中筛选项的情况
            if (selectedChipId == View.NO_ID) {
                // 如果是从工单管理菜单进入，并且没有选中任何筛选项，则加载全部工单
                // 不添加任何状态筛选条件
                Log.d("RepairOrders", "没有选中任何筛选项，加载全部工单")
                view?.findViewById<TextView>(R.id.tv_current_filter)?.text = "当前：全部工单 (加载中...)"
            } else {
                // 有选中筛选项的情况，按原逻辑处理
                when (selectedChipId) {
                    R.id.chip_uncompleted -> {
                        statusArray.add("pending_orders")
                        statusArray.add("engineer_receive")
                        statusArray.add("engineer_departure")
                        statusArray.add("engineer_arrive")
                        statusArray.add("wait_confirmed_report")
                        statusArray.add("to_be_settled")
                    }
                    R.id.chip_pending -> {
                        statusArray.add("pending_orders")
                    }
                    R.id.chip_processing -> {
                        statusArray.add("engineer_receive")
                        statusArray.add("engineer_departure")
                        statusArray.add("engineer_arrive")
                    }
                    R.id.chip_waiting_confirm -> {
                        statusArray.add("wait_confirmed_report")
                        statusArray.add("to_be_settled")
                    }
                }
                if (statusArray.size() > 0) {
                    add("status", statusArray)
                }
            }
        }
        Log.d("RepairOrders", "发送API请求，参数: $jsonObject")
        val call = workOrderApi.getWorkOrderListWithArrayParams(jsonObject)
        // 发起请求
        call.enqueue(object : Callback<WorkOrderResponse> {
            override fun onResponse(call: Call<WorkOrderResponse>, response: Response<WorkOrderResponse>) {
                isLoading = false
                swipeRefreshLayout.isRefreshing = false
                if (response.isSuccessful) {
                    val workOrderResponse = response.body()
                    if (workOrderResponse != null && workOrderResponse.code == 200) {
                        Log.d("RepairOrders", "获取工单列表成功，返回${workOrderResponse.data?.rows?.size ?: 0}条数据")
                        // 获取工单数据
                        val workOrderCount = workOrderResponse.data?.total ?: 0
                        viewModel.totalItems = workOrderCount // 设置总条目数，用于分页判断
                        totalItems = workOrderCount // 保持局部变量同步
                        val orderList = workOrderResponse.data?.rows ?: emptyList()
                        Log.d("RepairOrders", "总工单数: ${viewModel.totalItems}, 当前页: ${viewModel.currentPage}")
                        try {
                            // 转换为RepairOrder对象
                            val orders = mapToRepairOrders(orderList)
                            // 如果是第一页，直接替换，否则添加到现有数据
                            if (viewModel.currentPage == 1) {
                                viewModel.allOrders.value = orders
                            } else {
                                viewModel.allOrders.value = viewModel.allOrders.value?.toMutableList()?.apply {
                                    addAll(orders)
                                } ?: orders
                            }
                            // 应用当前过滤器
                            applyFilters()
                            // 如果没有结果，显示提示消息
                            val searchText = searchEdit.text.toString()
                            if (viewModel.filteredOrders.value?.isEmpty() == true) {
                                if (searchText.isNotEmpty()) {
                                    Toast.makeText(requireContext(), "没有找到匹配\"$searchText\"的工单", Toast.LENGTH_SHORT).show()
                                } else {
                                    emptyView.visibility = View.VISIBLE
                                }
                            }
                        } catch (e: Exception) {
                            Log.e("RepairOrders", "数据解析错误", e)
                            Toast.makeText(requireContext(), "数据解析错误: ${e.message}", Toast.LENGTH_SHORT).show()
                            // 显示空状态
                            if (viewModel.allOrders.value?.isEmpty() == true) {
                                emptyView.visibility = View.VISIBLE
                            }
                        }
                        viewModel.isDataLoaded = true
                    } else {
                        // API返回错误
                        val errorMsg = workOrderResponse?.msg ?: workOrderResponse?.message ?: "未知错误"
                        Log.e("RepairOrders", "API返回错误: $errorMsg")
                        // 处理401错误，会话过期
                        if (workOrderResponse?.code == 401) {
                            handleSessionExpired()
                        } else {
                            Toast.makeText(requireContext(), "获取工单列表失败: $errorMsg", Toast.LENGTH_SHORT).show()
                        }
                        // 显示空状态
                        if (viewModel.allOrders.value?.isEmpty() == true) {
                            emptyView.visibility = View.VISIBLE
                        }
                    }
                } else {
                    // HTTP错误
                    Log.e("RepairOrders", "HTTP错误: ${response.code()}")
                    // 处理401错误，会话过期
                    if (response.code() == 401) {
                        handleSessionExpired()
                    } else {
                        Toast.makeText(requireContext(), "获取工单列表失败: HTTP错误 ${response.code()}", Toast.LENGTH_SHORT).show()
                    }
                    // 显示空状态
                    if (viewModel.allOrders.value?.isEmpty() == true) {
                        emptyView.visibility = View.VISIBLE
                    }
                }
            }
            override fun onFailure(call: Call<WorkOrderResponse>, t: Throwable) {
                isLoading = false
                swipeRefreshLayout.isRefreshing = false
                Log.e("RepairOrders", "网络错误", t)
                Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
                // 显示空状态
                if (viewModel.allOrders.value?.isEmpty() == true) {
                    emptyView.visibility = View.VISIBLE
                }
            }
        })
    }

    private fun loadMoreOrders() {
        if (isLoading) return
        // 安全检查：确保Fragment仍然附加到上下文
        if (!isAdded) {
            Log.d("RepairOrders", "加载更多工单时Fragment已分离")
            return
        }
        isLoading = true
        swipeRefreshLayout.isRefreshing = true
        // 构建请求参数，使用JsonObject支持数组类型参数
        val jsonObject = JsonObject().apply {
            addProperty("pageNumber", (viewModel.currentPage + 1).toString())
            addProperty("pageSize", pageSize.toString())
            // 获取用户ID
            val tokenPrefs = requireContext().getSharedPreferences("token_pref", android.content.Context.MODE_PRIVATE)
            val userId = tokenPrefs.getString("userId", "") ?: ""
            // 工程师筛选
            if (filterEngineerId != null) {
                addProperty("engineerId", filterEngineerId)
            } else if (currentTab == 2 || currentTab == 3) {
                if (userId.isNotEmpty()) {
                    addProperty("engineerId", userId)
                }
            }
            // 状态筛选
            val selectedChipId = chipGroup.checkedChipId
            val statusArray = JsonArray()

            // 处理没有选中筛选项的情况
            if (selectedChipId == View.NO_ID) {
                // 如果是从工单管理菜单进入，并且没有选中任何筛选项，则加载全部工单
                // 不添加任何状态筛选条件
                Log.d("RepairOrders", "加载更多时没有选中任何筛选项，加载全部工单")
            } else {
                // 有选中筛选项的情况，按原逻辑处理
                when (selectedChipId) {
                    R.id.chip_uncompleted -> {
                        statusArray.add("pending_orders")
                        statusArray.add("engineer_receive")
                        statusArray.add("engineer_departure")
                        statusArray.add("engineer_arrive")
                        statusArray.add("wait_confirmed_report")
                        statusArray.add("to_be_settled")
                    }
                    R.id.chip_pending -> {
                        statusArray.add("pending_orders")
                    }
                    R.id.chip_processing -> {
                        statusArray.add("engineer_receive")
                        statusArray.add("engineer_departure")
                        statusArray.add("engineer_arrive")
                    }
                    R.id.chip_waiting_confirm -> {
                        statusArray.add("wait_confirmed_report")
                        statusArray.add("to_be_settled")
                    }
                }
                if (statusArray.size() > 0) {
                    add("status", statusArray)
                }
            }
        }
        Log.d("RepairOrders", "加载更多，发送API请求，参数: $jsonObject")
        val call = workOrderApi.getWorkOrderListWithArrayParams(jsonObject)
        call.enqueue(object : Callback<WorkOrderResponse> {
            override fun onResponse(call: Call<WorkOrderResponse>, response: Response<WorkOrderResponse>) {
                isLoading = false
                swipeRefreshLayout.isRefreshing = false
                if (response.isSuccessful) {
                    val workOrderResponse = response.body()
                    if (workOrderResponse?.code == 200) {
                        val newOrders = mapToRepairOrders(workOrderResponse.data?.rows ?: emptyList())
                        Log.d("RepairOrders", "加载更多成功，获取${newOrders.size}条新数据")
                        val updatedOrders = viewModel.allOrders.value?.toMutableList() ?: mutableListOf()
                        updatedOrders.addAll(newOrders)
                        viewModel.allOrders.value = updatedOrders
                        // 更新总数（防止服务端数据变化）
                        viewModel.totalItems = workOrderResponse.data?.total ?: viewModel.totalItems
                        totalItems = viewModel.totalItems
                        viewModel.filteredOrders.value = filterOrdersByChipAndSearch()
                        adapter.updateData(viewModel.filteredOrders.value ?: emptyList())
                        viewModel.currentPage++
                        updateFilterTextWithCount()
                        Log.d("RepairOrders", "加载更多完成，当前页: ${viewModel.currentPage}, 已加载${viewModel.allOrders.value?.size ?: 0}条，总共${viewModel.totalItems}条")
                    } else {
                        if (workOrderResponse?.code == 401) {
                            handleSessionExpired()
                        } else {
                            Toast.makeText(requireContext(), "加载更多工单失败: ${workOrderResponse?.msg ?: workOrderResponse?.message ?: "未知错误"}", Toast.LENGTH_SHORT).show()
                        }
                    }
                } else {
                    if (response.code() == 401) {
                        handleSessionExpired()
                    } else {
                        Toast.makeText(requireContext(), "加载更多工单失败: ${response.message()}", Toast.LENGTH_SHORT).show()
                    }
                }
            }
            override fun onFailure(call: Call<WorkOrderResponse>, t: Throwable) {
                isLoading = false
                swipeRefreshLayout.isRefreshing = false
                Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    private fun filterOrders() {
        viewModel.filteredOrders.value = filterOrdersByChipAndSearch()
        adapter.updateData(viewModel.filteredOrders.value ?: emptyList())
        // 更新筛选文本，显示工单数量
        updateFilterTextWithCount()
    }

    private fun filterOrdersByChipAndSearch(): List<RepairOrder> {
        val searchText = searchEdit.text.toString().lowercase()
        val selectedChipId = chipGroup.checkedChipId

        // 创建状态计数Map
        val statusCounts = mutableMapOf<String, Int>()
        val statusValueCounts = mutableMapOf<String, Int>()

        // 首先统计所有工单状态
        viewModel.allOrders.value?.forEach { order ->
            // 统计文本状态
            statusCounts[order.status] = (statusCounts[order.status] ?: 0) + 1
            // 统计原始状态值
            statusValueCounts[order.statusValue] = (statusValueCounts[order.statusValue] ?: 0) + 1
        }

        // 日志输出状态统计信息
        Log.d("RepairOrderStats", "工单状态统计:")
        statusCounts.forEach { (status, count) ->
            Log.d("RepairOrderStats", "$status=$count")
        }
        Log.d("RepairOrderStats", "工单状态值统计:")
        statusValueCounts.forEach { (statusValue, count) ->
            Log.d("RepairOrderStats", "$statusValue=$count")
        }

        // 如果没有选中任何筛选项，显示全部工单
        if (selectedChipId == View.NO_ID) {
            val matchesSearch = searchText.isEmpty() || viewModel.allOrders.value?.any { order ->
                order.customerName.lowercase().contains(searchText) ||
                        order.machineModel.lowercase().contains(searchText) ||
                        order.code.lowercase().contains(searchText) ||
                        order.id.lowercase().contains(searchText)
            } == true

            // 只应用搜索筛选
            return if (searchText.isEmpty()) {
                viewModel.allOrders.value ?: emptyList()
            } else {
                viewModel.allOrders.value?.filter { order ->
                    order.customerName.lowercase().contains(searchText) ||
                            order.machineModel.lowercase().contains(searchText) ||
                            order.code.lowercase().contains(searchText) ||
                            order.id.lowercase().contains(searchText)
                } ?: emptyList()
            }
        }

        // 根据选中的Chip进行严格筛选
        return viewModel.allOrders.value?.filter { order ->
            // 搜索条件过滤
            val matchesSearch = searchText.isEmpty() ||
                    order.customerName.lowercase().contains(searchText) ||
                    order.machineModel.lowercase().contains(searchText) ||
                    order.code.lowercase().contains(searchText) ||
                    order.id.lowercase().contains(searchText)

            // 状态过滤 - 根据选中的Chip进行严格筛选
            val matchesStatus = when (selectedChipId) {
                R.id.chip_uncompleted -> {
                    // 未完成工单 - 不包含"已完成"、"已关闭"、"已取消"状态
                    // 需要包含"待接单"和"待分配"状态
                    order.statusValue != "completed" &&
                            order.statusValue != "close" &&
                            order.statusValue != "cancel" &&
                            !order.status.contains("完成") &&
                            !order.status.contains("关闭") &&
                            !order.status.contains("取消")
                }
                R.id.chip_pending -> {
                    // 待接单 - 严格匹配待接单状态
                    order.statusValue == "pending_orders" ||
                            order.status.contains("待接单") ||
                            order.status.contains("待分配")
                }
                R.id.chip_processing -> {
                    // 处理中 - 严格匹配处理中状态
                    order.statusValue == "engineer_receive" ||
                            order.statusValue == "engineer_departure" ||
                            order.statusValue == "engineer_arrive" ||
                            (order.status.contains("接单") && !order.status.contains("待接单")) ||
                            order.status.contains("出发") ||
                            order.status.contains("到达") ||
                            order.status.contains("路途") ||
                            order.status.contains("维修中") ||
                            order.status.contains("处理中")
                }
                R.id.chip_waiting_confirm -> {
                    // 待确认/结算 - 严格匹配待确认状态
                    order.statusValue == "wait_confirmed_report" ||
                            order.statusValue == "to_be_settled" ||
                            order.status.contains("待确认") ||
                            order.status.contains("等待确认") ||
                            order.status.contains("结算") ||
                            order.status.contains("待结算") ||
                            order.status.contains("待审核") ||
                            order.status.contains("审核")
                }
                R.id.chip_engineer_orders -> {
                    // 工程师工单 - 严格匹配指定工程师
                    if (filterEngineerId != null) {
                        order.engineerId == filterEngineerId
                    } else {
                        order.engineerId != null && order.engineerName?.isNotEmpty() == true
                    }
                }
                else -> true // 默认显示全部
            }

            // 增强日志记录，详细记录每个工单的筛选情况及状态信息
            val selectedChipText = when (selectedChipId) {
                R.id.chip_uncompleted -> "未完成"
                R.id.chip_pending -> "待接单"
                R.id.chip_processing -> "处理中"
                R.id.chip_waiting_confirm -> "待确认/结算"
                R.id.chip_engineer_orders -> "工程师工单"
                else -> "未知筛选项"
            }

            Log.d("RepairOrderFilter", "工单筛选：工单号=${order.code}, 状态=${order.status}, 状态值=${order.statusValue}, " +
                    "筛选项=$selectedChipText, 匹配结果=${if (matchesStatus) "通过" else "不通过"}")

            matchesSearch && matchesStatus
        }?.also { filteredList ->
            // 筛选结果统计
            Log.d("RepairOrderFilter", "筛选结果：共${filteredList.size}条工单，原始工单数量：${viewModel.allOrders.value?.size ?: 0}")
        } ?: emptyList()
    }

    // 将API返回的工单数据映射为应用中的RepairOrder对象
    private fun mapToRepairOrders(workOrders: List<WorkOrderItem>): List<RepairOrder> {
        return workOrders.map { item ->
            // 机器型号，优先使用productInfo字段，其次再使用brand/machine组合
            val deviceModel = when {
                !item.productInfo.isNullOrEmpty() -> item.productInfo
                !item.brand.isNullOrEmpty() && !item.machine.isNullOrEmpty() -> "${item.brand}/${item.machine}"
                !item.brand.isNullOrEmpty() -> item.brand
                !item.machine.isNullOrEmpty() -> item.machine
                !item.deviceGroup?.label.isNullOrEmpty() -> item.deviceGroup?.label ?: ""
                else -> ""
            }

            // 记录日志，包含所有相关字段便于调试
            Log.d("RepairOrderData", "处理工单数据：ID=${item.id}, 工单号=${item.code}, 客户=${item.customerName}, " +
                    "机型=${deviceModel}, 设备组=${item.deviceGroup?.label ?: ""}, " +
                    "服务类型=${item.serType?.label ?: item.serType?.value ?: ""}, " +
                    "工程师=${item.engineerId?.name ?: "未分配"}, " +
                    "productId=${item.productId ?: "无"}, " +
                    "productInfo=${item.productInfo ?: "无"}")

            RepairOrder(
                id = item.id,             // 使用API返回的id
                code = item.code,         // 工单号
                customerId = item.customerId ?: "",
                customerName = item.customerName ?: "",
                latitude = item.latitude ?: 0.0,
                longitude = item.longitude ?: 0.0,
                engineerId = item.engineerId?.id,
                engineerName = item.engineerId?.name,
                status = item.status?.label ?: "未知状态",
                statusValue = item.status?.value ?: "",
                createTime = parseDateTime(item.createdAt),
                startTime = if (item.beginTime != null) parseDateTime(item.beginTime) else null,
                completedAt = parseDateTime(item.completedAt),
                machineNumber = item.deviceNumber ?: "",
                machineModel = deviceModel,
                deviceGroup = item.deviceGroup?.label ?: "",
                serviceType = item.serType?.label ?: item.serType?.value ?: "", // 直接使用API返回的服务类型文本
                problemDesc = item.excDesc ?: "",
                // 添加费用信息
                visitPay = item.visitPay ?: 0.0,
                longWayVisitPay = item.longWayVisitPay ?: 0.0,
                repairPay = item.repairPay ?: 0.0,
                itemPay = item.itemPay ?: 0.0,
                actualReplacePay = item.actualReplacePay ?: 0.0,
                engineerAdditionalPay = item.engineerAdditionalPay ?: 0.0,
                derateAmount = item.derateAmount ?: 0.0,
                discountAmount = item.discountAmount ?: 0.0,
                additionalPay = item.additionalPay ?: 0.0,
                totalPay = item.totalPay ?: 0.0,
                actualPay = item.actualPay ?: 0.0,
                // 添加时间信息
                expectArriveTime = item.expectArriveTime ?: "",
                prospectArriveTime = item.prospectArriveTime ?: "",
                arriveTime = item.arriveTime,
                waitConfirmTime = item.waitConfirmTime,
                finishTime = item.finishTime,
                // 工单状态时间字段
                receiveTime = parseDateTime(item.orderReceiveTime),
                departureTime = parseDateTime(item.departureTime),
                actualArriveTime = parseDateTime(item.actualArriveTime),
                sendReportTime = parseDateTime(item.sendReportTime),
                confirmReportTime = parseDateTime(item.confirmReportTime),
                productId = item.productId,  // 添加productId字段映射
            )
        }
    }

    private fun parseDateTime(dateTimeString: String?): Date {
        if (dateTimeString.isNullOrEmpty()) {
            return Calendar.getInstance().time
        }

        return try {
            // 尝试常见的日期格式
            val possibleFormats = arrayOf(
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd HH:mm",
                "yyyy-MM-dd'T'HH:mm:ss",
                "yyyy-MM-dd'T'HH:mm:ss.SSS",
                "yyyy-MM-dd'T'HH:mm:ss.SSSZ",
                "yyyy/MM/dd HH:mm:ss",
                "yyyy/MM/dd HH:mm"
            )


            // 依次尝试各种格式
            for (format in possibleFormats) {
                try {
                    val sdf = SimpleDateFormat(format, Locale.getDefault())
                    val parsedDate = sdf.parse(dateTimeString)
                    if (parsedDate != null) {
                        return parsedDate
                    }
                } catch (e: Exception) {
                    // 继续尝试下一种格式
                }
            }

            // 如果所有格式都失败，记录错误并返回当前时间
            Calendar.getInstance().time
        } catch (e: Exception) {
            Calendar.getInstance().time
        }
    }

    private fun applyFilters() {
        viewModel.filteredOrders.value = filterOrdersByChipAndSearch()
        Log.d("RepairOrders", "应用过滤后，显示${viewModel.filteredOrders.value?.size ?: 0}条工单")

        // 更新适配器
        adapter.updateData(viewModel.filteredOrders.value ?: emptyList())

        restoreScrollPosition()

        // 更新筛选文本，显示工单数量
        updateFilterTextWithCount()

        // 更新空视图显示
        if (viewModel.filteredOrders.value.isNullOrEmpty()) {
            emptyView.visibility = View.VISIBLE
        } else {
            emptyView.visibility = View.GONE
        }

        // 注意：这里不要增加currentPage，因为这是在过滤时调用，不是加载新数据
    }

    // 更新筛选文本，显示工单数量
    private fun updateFilterTextWithCount() {
        val tvCurrentFilter = view?.findViewById<TextView>(R.id.tv_current_filter) ?: return
        val selectedChipId = chipGroup.checkedChipId

        // 获取当前筛选类型文本
        val filterText = when (selectedChipId) {
            R.id.chip_uncompleted -> "未完成"
            R.id.chip_pending -> "待接单"
            R.id.chip_processing -> "处理中"
            R.id.chip_waiting_confirm -> "待确认/结算"
            R.id.chip_engineer_orders -> {
                if (filterEngineerId != null) "工程师：${view?.findViewById<Chip>(R.id.chip_engineer_orders)?.text}" else "工程师工单"
            }
            View.NO_ID -> "全部工单" // 没有选中任何筛选项时显示"全部工单"
            else -> "全部工单"
        }

        // 更新文本，包含工单数量
        tvCurrentFilter.text = "当前：$filterText (${viewModel.filteredOrders.value?.size ?: 0}条工单)"
    }

    // 处理会话过期的方法
    private fun handleSessionExpired() {
        Log.e("RepairOrders", "会话已过期，将由TokenInterceptor处理刷新令牌")

        // 安全检查：确保Fragment仍然附加到上下文
        if (!isAdded) {
            Log.d("RepairOrders", "Fragment已分离，不处理会话过期")
            return
        }

        try {
            Toast.makeText(requireContext(), "会话已过期，正在尝试刷新...", Toast.LENGTH_SHORT).show()

            // 在组件中不需要处理令牌刷新，TokenInterceptor会自动处理
            // 只需重新加载数据
            Handler(Looper.getMainLooper()).postDelayed({
                // 再次检查Fragment是否仍然附加到上下文
                if (isAdded) {
                    // 延迟一秒后刷新数据，给TokenInterceptor时间刷新令牌
                    refreshData()
                } else {
                    Log.d("RepairOrders", "延迟执行时Fragment已分离，取消刷新操作")
                }
            }, 1000)
        } catch (e: Exception) {
            Log.e("RepairOrders", "处理会话过期时发生错误: ${e.message}", e)
        }
    }

    // 刷新数据
    private fun refreshData() {
        // 安全检查：确保Fragment仍然附加到上下文
        if (!isAdded) {
            Log.d("RepairOrders", "刷新数据时Fragment已分离")
            return
        }

        // 重置页码和总数
        viewModel.currentPage = 1
        viewModel.totalItems = 0
        totalItems = 0

        // 清空已加载的工单
        viewModel.allOrders.value = emptyList()
        viewModel.filteredOrders.value = emptyList()

        // 通知适配器数据已更改
        adapter.notifyDataSetChanged()

        // 重新加载数据
        loadWorkOrders()
    }

    /**
     * 显示工程师选择对话框，用于按工程师筛选工单
     */
    private fun showEngineerSelectionForFiltering() {
        workOrderApi.getEngineerList("0").enqueue(object : Callback<EngineerListResponse> {
            override fun onResponse(call: Call<EngineerListResponse>, response: Response<EngineerListResponse>) {
                if (response.isSuccessful && response.body()?.code == 200) {
                    val engineers = response.body()?.getEngineerList() ?: emptyList()
                    if (engineers.isNotEmpty()) {
                        val engineerNames = engineers.map { it.name }.toTypedArray()

                        AlertDialog.Builder(requireContext())
                            .setTitle("选择工程师")
                            .setItems(engineerNames) { _, which ->
                                val selectedEngineer = engineers[which]
                                filterEngineerId = selectedEngineer.id
                                // 更新Chip的显示文本
                                val engineerChip = view?.findViewById<Chip>(R.id.chip_engineer_orders)
                                engineerChip?.text = "工程师: ${selectedEngineer.name}"
                                // 重新加载工单并应用筛选
                                loadWorkOrders()
                            }
                            .setNegativeButton("取消") { _, _ ->
                                // 取消选择时重置到全部
                                chipGroup.check(R.id.chip_all)
                                filterEngineerId = null
                            }
                            .setCancelable(false)
                            .show()
                    } else {
                        Toast.makeText(requireContext(), "没有可用的工程师", Toast.LENGTH_SHORT).show()
                        chipGroup.check(R.id.chip_all)
                        filterEngineerId = null
                    }
                } else {
                    Toast.makeText(requireContext(), "获取工程师列表失败", Toast.LENGTH_SHORT).show()
                    chipGroup.check(R.id.chip_all)
                    filterEngineerId = null
                }
            }

            override fun onFailure(call: Call<EngineerListResponse>, t: Throwable) {
                Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
                chipGroup.check(R.id.chip_all)
                filterEngineerId = null
            }
        })
    }

    /**
     * 直接使用指定的工程师ID进行筛选
     */
    private fun showEngineerSelectionForFiltering(engineerId: String) {
        filterEngineerId = engineerId

        // 更新Chip的显示文本
        val engineerChip = view?.findViewById<Chip>(R.id.chip_engineer_orders)
        engineerChip?.text = "当前工程师的工单"

        // 更新当前筛选条件文本，数量将在加载后更新
        view?.findViewById<TextView>(R.id.tv_current_filter)?.text = "当前：工程师工单 (加载中...)"

        // 重置页码和数据
        viewModel.currentPage = 1
        viewModel.totalItems = 0
        totalItems = 0
        viewModel.allOrders.value = emptyList()
        viewModel.filteredOrders.value = emptyList()

        // 重新加载工单并应用筛选
        loadWorkOrders()
    }

    private fun restoreScrollPosition() {
        val layoutManager = recyclerView.layoutManager as? LinearLayoutManager ?: return
        if (listPosition >= 0) {
            layoutManager.scrollToPositionWithOffset(listPosition, listOffset)
            Log.d("RepairOrders", "恢复列表位置: position=$listPosition, offset=$listOffset")
        }
    }

    // 显示分配工程师对话框
    private fun showAssignEngineerDialog(orderId: String, productId: String) {
        currentWorkOrderId = orderId
        // 确保productId不为空
        if (productId.isBlank()) {
            Toast.makeText(requireContext(), "工单缺少产品ID，无法分配工程师", Toast.LENGTH_SHORT).show()
            return
        }
        Log.d("RepairOrders", "获取工程师列表，工单ID: $orderId, 产品ID: $productId")
        // 调用API获取可用工程师列表
        workOrderApi.getEngineerList(productId).enqueue(object : Callback<EngineerListResponse> {
            override fun onResponse(call: Call<EngineerListResponse>, response: Response<EngineerListResponse>) {
                Log.d("RepairOrders", "工程师列表API响应: ${response.code()}")
                if (response.isSuccessful) {
                    try {
                        val engineerListResponse = response.body()
                        Log.d("RepairOrders", "工程师列表原始响应: $engineerListResponse")
                        if (engineerListResponse?.code == 200) {
                            val engineerList = engineerListResponse.getEngineerList()
                            if (engineerList.isNotEmpty()) {
                                Log.d("RepairOrders", "获取工程师列表成功，数量: ${engineerList.size}")
                                showEngineerSelectionDialog(engineerList)
                            } else {
                                Log.e("RepairOrders", "工程师列表为空")
                                Toast.makeText(requireContext(), "没有可用的工程师", Toast.LENGTH_SHORT).show()
                            }
                        } else {
                            Log.e("RepairOrders", "API返回错误: ${engineerListResponse?.msg ?: engineerListResponse?.message ?: "未知错误"}")
                            Toast.makeText(requireContext(), "获取工程师列表失败: ${engineerListResponse?.msg ?: engineerListResponse?.message ?: "未知错误"}", Toast.LENGTH_SHORT).show()
                        }
                    } catch (e: Exception) {
                        Log.e("RepairOrders", "解析工程师列表响应异常", e)
                        Toast.makeText(requireContext(), "数据解析错误: ${e.message}", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    try {
                        val errorBody = response.errorBody()?.string() ?: ""
                        Log.e("RepairOrders", "获取工程师列表失败: ${response.code()} - ${response.message()}, 详情: $errorBody")
                        if (response.code() == 401 || errorBody.contains("会话") || errorBody.contains("session")) {
                            handleSessionExpired()
                        } else {
                            Toast.makeText(requireContext(), "获取工程师列表失败: ${response.message()}", Toast.LENGTH_SHORT).show()
                        }
                    } catch (e: Exception) {
                        Log.e("RepairOrders", "处理错误响应异常", e)
                        Toast.makeText(requireContext(), "获取工程师列表失败: ${response.message()}", Toast.LENGTH_SHORT).show()
                    }
                }
            }
            override fun onFailure(call: Call<EngineerListResponse>, t: Throwable) {
                Log.e("RepairOrders", "获取工程师列表网络请求失败", t)
                Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    // 显示工程师选择对话框
    private fun showEngineerSelectionDialog(engineers: List<Engineer>) {
        // 完整数据与展示数据分离，便于搜索过滤且保留详情信息
        val fullEngineers = engineers.toMutableList()
        val displayedEngineers = fullEngineers.toMutableList()
        val dialogView = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_engineer_selection, null)
        val recyclerView = dialogView.findViewById<RecyclerView>(R.id.recycler_engineers)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        val adapter = EngineerAdapter(displayedEngineers) { engineer ->
            confirmAssignEngineer(engineer)
        }
        recyclerView.adapter = adapter

        // 搜索功能
        dialogView.findViewById<EditText?>(R.id.edit_search_engineer)?.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: android.text.Editable?) {
                val key = s?.toString()?.trim()?.lowercase() ?: ""
                val filtered = if (key.isEmpty()) fullEngineers else fullEngineers.filter {
                    it.name.lowercase().contains(key) || (it.phone?.contains(key) == true)
                }
                displayedEngineers.clear(); displayedEngineers.addAll(filtered)
                adapter.notifyDataSetChanged()
            }
        })

        // ====== 并发加载工程师详情 ======
        lifecycleScope.launch {
            val dispatcher = Dispatchers.IO.limitedParallelism(5)
            fullEngineers.forEachIndexed { idx, engineer ->
                launch(dispatcher) {
                    val phone = try {
                        val resp = workOrderApi.getEngineer(engineer.id).execute()
                        if (resp.isSuccessful && resp.body()?.code == 200) {
                            (resp.body()?.data as? Map<*, *>)?.let { (it["userManageVo"] as? Map<*, *>)?.get("mobileNumber")?.toString() }
                        } else null
                    } catch (e: Exception) { null }
                    val workData = try {
                        val resp = workOrderApi.getEngineerWorkSummary(engineer.id).execute()
                        if (resp.isSuccessful && resp.body()?.code == 200) resp.body()?.data as? EngineerWorkData else null
                    } catch (e: Exception) { null }
                    val updated = engineer.copy(phone = phone)
                    withContext(Dispatchers.Main) {
                        fullEngineers[idx] = updated
                        val posDisp = displayedEngineers.indexOfFirst { it.id == updated.id }
                        if (posDisp != -1) {
                            displayedEngineers[posDisp] = updated
                            adapter.notifyItemChanged(posDisp)
                        }
                        if (workData != null) adapter.updateEngineerWorkData(updated.id, workData)
                    }
                }
            }
        }
        // ====== 并发加载工程师详情 END ======

        AlertDialog.Builder(requireContext())
            .setTitle("选择工程师")
            .setView(dialogView)
            .setNegativeButton("取消", null)
            .show()
    }

    // 确认分配工程师
    private fun confirmAssignEngineer(engineer: Engineer) {
        AlertDialog.Builder(requireContext())
            .setTitle("确认分配")
            .setMessage("确定将该维修工单分配给${engineer.name}吗？")
            .setPositiveButton("确定") { _, _ ->
                assignEngineerToOrder(engineer)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    // 执行分配操作
    private fun assignEngineerToOrder(engineer: Engineer) {
        val params = HashMap<String, String>()
        params["id"] = currentWorkOrderId ?: return
        params["engineerId"] = engineer.id
        Log.d("RepairOrders", "分配工单参数：id=${params["id"]}, engineerId=${params["engineerId"]}")
        workOrderApi.confirmAssignWorkOrder(params).enqueue(object : Callback<ApiResponse<Any>> {
            override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                Log.d("RepairOrders", "分配工单响应状态码: ${response.code()}")
                if (response.isSuccessful) {
                    val apiResponse = response.body()
                    Log.d("RepairOrders", "分配工单响应: ${apiResponse?.code} - ${apiResponse?.msg}")
                    if (apiResponse?.code == 200) {
                        Toast.makeText(requireContext(), "分配成功", Toast.LENGTH_SHORT).show()
                        loadWorkOrders()
                    } else {
                        val errorMsg = apiResponse?.msg ?: "未知错误"
                        Log.e("RepairOrders", "分配失败: $errorMsg")
                        if (errorMsg.contains("会话") || apiResponse?.code == 401) {
                            handleSessionExpired()
                        } else {
                            Toast.makeText(requireContext(), "分配失败: $errorMsg", Toast.LENGTH_SHORT).show()
                        }
                    }
                } else {
                    try {
                        val errorBody = response.errorBody()?.string() ?: ""
                        Log.e("RepairOrders", "分配工单API错误: ${response.code()} - ${response.message()}, 详情: $errorBody")
                        if (response.code() == 401 || errorBody.contains("会话") || errorBody.contains("session")) {
                            handleSessionExpired()
                        } else {
                            Toast.makeText(requireContext(), "分配失败: ${response.message()}", Toast.LENGTH_SHORT).show()
                        }
                    } catch (e: Exception) {
                        Log.e("RepairOrders", "处理分配工单错误响应异常", e)
                        Toast.makeText(requireContext(), "分配失败: ${response.message()}", Toast.LENGTH_SHORT).show()
                    }
                }
            }
            override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                Log.e("RepairOrders", "分配工单网络请求失败", t)
                Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
}

data class EngineerListResponse(
    val code: Int,
    val msg: String? = null,
    val message: String? = null,
    val data: Any? = null
) {
    // 自定义toString()方法以方便日志输出
    override fun toString(): String {
        val engineerCount = when (data) {
            is EngineerPageData -> data.rows?.size ?: 0
            is List<*> -> (data as? List<*>)?.size ?: 0
            else -> 0
        }
        return "EngineerListResponse(code=$code, msg='${msg ?: message ?: ""}', data=${engineerCount}个工程师)"
    }

    // 解析工程师列表的辅助方法
    fun getEngineerList(): List<Engineer> {
        return when (data) {
            // 第一种格式：data是EngineerPageData对象
            is EngineerPageData -> data.rows?.filter { it.id.isNotBlank() } ?: emptyList()

            // 第二种格式：data直接是Engineer列表
            is List<*> -> {
                try {
                    // 尝试将列表转换为Engineer对象列表
                    (data as? List<*>)?.mapNotNull { item ->
                        if (item is Engineer) {
                            item
                        } else if (item is Map<*, *>) {
                            // 如果是Map<*,*>，尝试手动转换为Engineer对象
                            val id = item["id"]?.toString() ?: ""
                            if (id.isBlank()) return@mapNotNull null

                            val name = item["name"]?.toString() ?: ""
                            val skillExpMap = item["skillExp"] as? Map<*, *>
                            val skillExp = if (skillExpMap != null) {
                                com.example.repairorderapp.model.DictItem(
                                    skillExpMap["value"]?.toString() ?: "",
                                    skillExpMap["label"]?.toString() ?: ""
                                )
                            } else null

                            Engineer(
                                id = id,
                                name = name,
                                skillExp = skillExp
                            )
                        } else null
                    } ?: emptyList()
                } catch (e: Exception) {
                    Log.e("EngineerListResponse", "解析工程师列表异常: ${e.message}")
                    emptyList()
                }
            }

            // 其他情况
            else -> emptyList()
        }
    }
}

/**
 * 工程师分页数据
 */
data class EngineerPageData(
    val total: String? = null,  // 总记录数
    val rows: List<Engineer>? = null  // 工程师列表
)

data class ApiResponse<T>(
    val code: Int,
    val msg: String? = null,
    val message: String? = null,
    val data: T?
) 