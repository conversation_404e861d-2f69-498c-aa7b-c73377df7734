package com.example.repairorderapp.ui.warehouse

import android.app.AlertDialog
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.*
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import android.widget.EditText
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.SearchView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.example.repairorderapp.R
import com.example.repairorderapp.data.api.ApiClient
import com.example.repairorderapp.data.api.ApiResponse
import com.example.repairorderapp.databinding.FragmentWarehouseBinding
import com.example.repairorderapp.model.PagedData
import com.example.repairorderapp.model.warehouse.WarehouseFilter
import com.example.repairorderapp.model.warehouse.WarehouseItem
import com.example.repairorderapp.model.warehouse.CategoryItem
import com.example.repairorderapp.model.warehouse.ProductItem
import com.example.repairorderapp.ui.common.EmptyView
import com.example.repairorderapp.util.SharedPrefsManager
import com.google.android.material.card.MaterialCardView
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

/**
 * 工程师个人仓库页面
 */
class WarehouseFragment : Fragment() {

    private var _binding: FragmentWarehouseBinding? = null
    private val binding get() = _binding ?: throw IllegalStateException("访问binding前必须先检查_binding是否为null")
    
    // 筛选卡片相关绑定
    private lateinit var filterCard: MaterialCardView
    private lateinit var autoCompleteProduct: AutoCompleteTextView
    private lateinit var autoCompleteCategory: AutoCompleteTextView
    private lateinit var autoCompletePartCategory: AutoCompleteTextView
    private lateinit var autoCompleteUnit: AutoCompleteTextView
    private lateinit var inputOem: EditText
    
    private lateinit var adapter: WarehouseAdapter
    private var currentPage = 1
    private val pageSize = PaginationScrollListener.PAGE_SIZE
    private var isLoading = false
    private var isLastPage = false
    
    // 筛选条件
    private var categoryId: String? = null
    private var partType: String? = null
    private var unitType: String? = null
    
    // 筛选相关
    private var currentFilter = WarehouseFilter()
    private var productList = listOf<Pair<String, String>>()
    private val productNameMap = mutableMapOf<String, String>()
    private var categoryList = listOf<Pair<String, String>>()
    private var partCategoryList = listOf<Pair<String, String>>()
    private var unitList = listOf<Pair<String, String>>()
    
    // 标记分类数据是否已加载
    private var filterDataLoaded = false
    
    // 在WarehouseFragment类中添加这些属性
    private var partCategoryTagId: String = ""
    private var unitTagId: String = ""
    
    // 添加一个标志，表示Fragment是否已经被销毁
    private var isFragmentDestroyed = false
    
    // 添加搜索协程作用域，便于统一管理
    private var searchCoroutineScope = CoroutineScope(Dispatchers.Main + Job())
    
    companion object {
        private const val TAG = "WarehouseFragment"
    }
    
    override fun onCreateView(
        inflater: LayoutInflater, 
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentWarehouseBinding.inflate(inflater, container, false)
        isFragmentDestroyed = false
        
        // 确保销毁前的搜索协程已取消，创建新的搜索协程作用域
        searchCoroutineScope.cancel()
        searchCoroutineScope = CoroutineScope(Dispatchers.Main + Job())
        
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // 初始化筛选卡片控件
        initFilterCardViews()
        
        setupToolbar()
        setupRecyclerView()
        setupListeners()
        loadWarehouseItems()
        loadFilterOptions()
        // 进入页面时就加载筛选数据
        loadFilterData()
    }
    
    /**
     * 初始化筛选卡片控件
     */
    private fun initFilterCardViews() {
        // 直接通过ViewBinding访问筛选卡片视图
        filterCard = binding.filterCardContainer.filterCard
        
        // 初始化筛选卡片中的控件
        // 注意：适用机型选项已隐藏，但仍然需要初始化变量以避免空指针异常
        autoCompleteProduct = filterCard.findViewById(R.id.auto_complete_product)
        autoCompleteCategory = filterCard.findViewById(R.id.auto_complete_category)
        autoCompletePartCategory = filterCard.findViewById(R.id.auto_complete_part_category)
        autoCompleteUnit = filterCard.findViewById(R.id.auto_complete_unit)
        inputOem = filterCard.findViewById(R.id.input_oem)
        
        // 设置重置按钮点击事件
        filterCard.findViewById<View>(R.id.btn_reset).setOnClickListener {
            resetFilter()
        }
        
        // 设置搜索按钮点击事件
        filterCard.findViewById<View>(R.id.btn_search).setOnClickListener {
            applyFilter()
        }
    }
    
    /**
     * 设置工具栏
     */
    private fun setupToolbar() {
        binding.toolbar.setNavigationOnClickListener {
            findNavController().navigateUp()
        }
        
        // 设置筛选按钮点击事件
        binding.btnFilter.setOnClickListener {
            toggleFilterCard()
        }
    }
    
    /**
     * 切换筛选卡片的显示状态
     */
    private fun toggleFilterCard() {
        if (filterCard.visibility == View.VISIBLE) {
            filterCard.visibility = View.GONE
        } else {
            if (!filterDataLoaded) {
                Toast.makeText(context, "筛选数据加载中，请稍后再试", Toast.LENGTH_SHORT).show()
                return
            }
            setupFilterCard()
            filterCard.visibility = View.VISIBLE
        }
    }
    
    /**
     * 设置筛选卡片内容
     */
    private fun setupFilterCard() {
        // 隐藏适用机型选项
        val inputLayoutProduct = filterCard.findViewById<com.google.android.material.textfield.TextInputLayout>(R.id.input_layout_product)
        inputLayoutProduct.visibility = View.GONE
        
        // 添加根布局的触摸监听，使用触摸事件代替点击事件，避免点击动画
        val rootView = filterCard.findViewById<View>(R.id.filter_card_content) ?: filterCard
        rootView.setOnTouchListener { v, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                // 清除所有输入框的焦点
                clearAllFocus()
                
                // 隐藏键盘
                val imm = requireContext().getSystemService(android.content.Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
                imm.hideSoftInputFromWindow(rootView.windowToken, 0)
            }
            // 返回false，不消费事件，让子视图仍能接收点击事件
            false
        }
        
        // 适用机型
        val productAdapter = ArrayAdapter(
            requireContext(), 
            android.R.layout.simple_dropdown_item_1line,
            productList.map { it.second }
        )
        autoCompleteProduct.setAdapter(productAdapter)
        
        // 设置选择监听
        autoCompleteProduct.setOnItemClickListener { _, _, position, _ ->
            if (position < productList.size) {
                val selectedProduct = productList[position]
                currentFilter.productId = selectedProduct.first
                productNameMap[selectedProduct.first] = selectedProduct.second
                
                // 显示清除按钮
                showClearButton(autoCompleteProduct)
                
                // 选择后收起键盘
                val imm = requireContext().getSystemService(android.content.Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
                imm.hideSoftInputFromWindow(autoCompleteProduct.windowToken, 0)
                
                // 清除焦点
                autoCompleteProduct.clearFocus()
            }
        }
        
        // 移除旧的文本变更监听器，确保不会有多个监听器
        if (autoCompleteProduct.tag is TextWatcher) {
            autoCompleteProduct.removeTextChangedListener(autoCompleteProduct.tag as TextWatcher)
        }
        
        // 添加文本变更监听器，实现实时搜索
        val textWatcher = object : TextWatcher {
            private var searchJob: Job? = null
            
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            
            override fun afterTextChanged(s: Editable?) {
                val searchText = s?.toString()?.trim() ?: ""
                
                // 清除之前的搜索Job
                searchJob?.cancel()
                
                // 清空产品ID，因为搜索文本已变更
                if (searchText.isNotEmpty()) {
                    currentFilter.productId = ""
                    
                    // 显示清除按钮
                    showClearButton(autoCompleteProduct)
                } else {
                    // 隐藏清除按钮
                    autoCompleteProduct.setCompoundDrawables(null, null, null, null)
                }
                
                // 如果输入长度大于等于2，才开始搜索
                if (searchText.length >= 2) {
                    // 延迟300ms再执行搜索，避免频繁请求
                    searchJob = searchCoroutineScope.launch {
                        delay(300)
                        Log.d(TAG, "开始搜索机型: $searchText")
                        searchProductModels(searchText)
                    }
                }
            }
        }
        
        autoCompleteProduct.addTextChangedListener(textWatcher)
        // 保存监听器引用，以便以后可以移除
        autoCompleteProduct.tag = textWatcher
        
        // 添加焦点变化监听
        autoCompleteProduct.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                // 获得焦点时，显示清除按钮（如果有文本）
                if (autoCompleteProduct.text?.isNotEmpty() == true) {
                    showClearButton(autoCompleteProduct)
                }
                
                // 如果有已加载的数据，直接显示下拉列表
                if (productList.isNotEmpty()) {
                    autoCompleteProduct.showDropDown()
                    Log.d(TAG, "焦点获取时显示${productList.size}个机型下拉选项")
                }
            } else {
                // 失去焦点时，隐藏清除按钮
                autoCompleteProduct.setCompoundDrawables(null, null, null, null)
            }
        }
        
        // 物品分类
        setupAutoCompleteAdapter(
            autoCompleteCategory,
            categoryList.map { it.second },
            { position -> 
                if (position < categoryList.size) {
                    val selectedCategory = categoryList[position]
                    if (selectedCategory.first.isEmpty()) {
                        // 选择"全部"选项，清除分类ID
                        currentFilter.categoryId = ""
                    } else {
                        currentFilter.categoryId = selectedCategory.first
                    }
                }
            }
        )
        
        // 零件分类
        setupAutoCompleteAdapter(
            autoCompletePartCategory,
            partCategoryList.map { it.second },
            { position -> 
                if (position < partCategoryList.size) {
                    val selectedPartCategory = partCategoryList[position]
                    if (selectedPartCategory.first.isEmpty()) {
                        // 选择"全部"选项，清除零件分类ID
                        currentFilter.partCategoryId = ""
                    } else {
                        currentFilter.partCategoryId = selectedPartCategory.first
                    }
                }
            }
        )
        
        // 所属单元
        setupAutoCompleteAdapter(
            autoCompleteUnit,
            unitList.map { it.second },
            { position -> 
                if (position < unitList.size) {
                    val selectedUnit = unitList[position]
                    if (selectedUnit.first.isEmpty()) {
                        // 选择"全部"选项，清除单元ID
                        currentFilter.unitId = ""
                    } else {
                        currentFilter.unitId = selectedUnit.first
                    }
                }
            }
        )
        
        // 设置当前选中值
        setFilterInitialValues()
    }
    
    /**
     * 显示清除按钮
     */
    private fun showClearButton(editText: android.widget.EditText) {
        val clearDrawable = ContextCompat.getDrawable(
            requireContext(),
            android.R.drawable.ic_menu_close_clear_cancel
        )
        clearDrawable?.setBounds(0, 0, clearDrawable.intrinsicWidth/2, clearDrawable.intrinsicHeight/2)
        
        editText.setCompoundDrawables(null, null, clearDrawable, null)
        
        // 设置点击监听
        editText.setOnTouchListener { v, event ->
            if (event.action == android.view.MotionEvent.ACTION_UP) {
                if (event.rawX >= (editText.right - editText.compoundDrawables[2].bounds.width() - editText.paddingEnd)) {
                    // 点击了清除按钮
                    editText.text?.clear()
                    // 隐藏清除按钮
                    editText.setCompoundDrawables(null, null, null, null)
                    // 清空选中的产品ID
                    currentFilter.productId = ""
                    return@setOnTouchListener true
                }
            }
            return@setOnTouchListener false
        }
    }
    
    /**
     * 搜索机型模型
     */
    private fun searchProductModels(searchText: String) {
        if (searchText.isEmpty()) return
        
        // 安全检查
        if (!isAdded || isFragmentDestroyed) return
        
        // 添加详细日志
        Log.d(TAG, "执行机型搜索请求: $searchText")
        
        // 显示加载状态 - 添加一个小菊花指示器
        val progressIndicator = autoCompleteProduct.compoundDrawables[2]  // TRAILING 位置的drawable
        if (progressIndicator == null) {
            val loadingDrawable = ContextCompat.getDrawable(
                requireContext(), 
                android.R.drawable.progress_indeterminate_horizontal
            )
            loadingDrawable?.setBounds(0, 0, 24, 24)
            autoCompleteProduct.setCompoundDrawables(
                null, null, loadingDrawable, null
            )
        }
        
        // 调用API搜索机型
        ApiClient.workOrderApi.getProductList(
            name = searchText,
            pageNumber = 1,
            pageSize = 10 // 减少数量，提高响应速度
        ).enqueue(object : Callback<ApiResponse<PagedData<ProductItem>>> {
            override fun onResponse(
                call: Call<ApiResponse<PagedData<ProductItem>>>,
                response: Response<ApiResponse<PagedData<ProductItem>>>
            ) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                
                // 清除加载指示器
                autoCompleteProduct.setCompoundDrawables(null, null, null, null)
                
                if (response.isSuccessful && response.body()?.code == 200) {
                    Log.d(TAG, "机型搜索请求成功，开始处理结果")
                    response.body()?.data?.rows?.map { 
                        Pair(it.id, it.fullName)
                    }?.let { results ->
                        Log.d(TAG, "获取到 ${results.size} 个机型结果")
                        
                        // 更新机型数据
                        results.forEach { pair ->
                            productNameMap[pair.first] = pair.second
                        }
                        
                        // 重要：更新搜索结果列表
                        productList = results
                        
                        // 更新适配器数据
                        val adapter = ArrayAdapter(
                            requireContext(),
                            android.R.layout.simple_dropdown_item_1line,
                            productList.map { it.second }
                        )
                        autoCompleteProduct.setAdapter(adapter)
                        
                        // 重要：重新设置选择监听器，确保搜索结果中的选择也能正确处理
                        autoCompleteProduct.setOnItemClickListener { _, _, position, _ ->
                            if (position < productList.size) {
                                val selectedProduct = productList[position]
                                currentFilter.productId = selectedProduct.first
                                productNameMap[selectedProduct.first] = selectedProduct.second
                                
                                // 显示清除按钮
                                showClearButton(autoCompleteProduct)
                                
                                // 选择后收起键盘
                                val imm = requireContext().getSystemService(android.content.Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
                                imm.hideSoftInputFromWindow(autoCompleteProduct.windowToken, 0)
                                
                                // 清除焦点
                                autoCompleteProduct.clearFocus()
                            }
                        }
                        
                        // 显示下拉列表 - 如果结果非空并且文本框仍在焦点中
                        if (results.isNotEmpty() && autoCompleteProduct.hasFocus()) {
                            autoCompleteProduct.showDropDown()
                            Log.d(TAG, "显示机型下拉列表")
                        }
                        
                        // 如果没有结果，显示提示
                        if (results.isEmpty() && autoCompleteProduct.hasFocus()) {
                            val noResultAdapter = ArrayAdapter(
                                requireContext(),
                                android.R.layout.simple_dropdown_item_1line,
                                listOf("没有找到匹配的机型")
                            )
                            autoCompleteProduct.setAdapter(noResultAdapter)
                            autoCompleteProduct.showDropDown()
                            Log.d(TAG, "没有找到匹配的机型")
                        }
                    }
                } else {
                    // 处理API错误
                    Log.e(TAG, "搜索机型API错误: ${response.code()}")
                    
                    // 显示错误提示
                    if (autoCompleteProduct.hasFocus()) {
                        val errorAdapter = ArrayAdapter(
                            requireContext(),
                            android.R.layout.simple_dropdown_item_1line,
                            listOf("加载数据失败，请重试")
                        )
                        autoCompleteProduct.setAdapter(errorAdapter)
                        autoCompleteProduct.showDropDown()
                    }
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<PagedData<ProductItem>>>, t: Throwable) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                
                // 清除加载指示器
                autoCompleteProduct.setCompoundDrawables(null, null, null, null)
                
                // 记录错误
                Log.e(TAG, "搜索机型失败: ${t.message}")
                
                // 显示错误提示
                if (autoCompleteProduct.hasFocus()) {
                    val errorAdapter = ArrayAdapter(
                        requireContext(),
                        android.R.layout.simple_dropdown_item_1line,
                        listOf("网络错误，请检查网络连接")
                    )
                    autoCompleteProduct.setAdapter(errorAdapter)
                    autoCompleteProduct.showDropDown()
                }
            }
        })
    }
    
    /**
     * 设置AutoCompleteTextView的适配器
     */
    private fun setupAutoCompleteAdapter(
        autoCompleteTextView: AutoCompleteTextView,
        items: List<String>,
        onItemSelected: (Int) -> Unit
    ) {
        val adapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_dropdown_item_1line,
            items
        )
        autoCompleteTextView.setAdapter(adapter)
        autoCompleteTextView.setOnItemClickListener { _, _, position, _ ->
            onItemSelected(position)
        }
    }
    
    /**
     * 设置筛选初始值
     */
    private fun setFilterInitialValues() {
        // 适用机型 - 由于已隐藏，不需要设置初始值
        /*
        if (currentFilter.productId.isNotEmpty()) {
            val productName = productNameMap[currentFilter.productId] ?: ""
            if (productName.isNotEmpty()) {
                autoCompleteProduct.setText(productName, false)
            } else {
                // 如果没有找到产品名称，说明可能数据有问题，清除选择
                autoCompleteProduct.text?.clear()
                currentFilter.productId = ""
            }
        } else {
            autoCompleteProduct.text?.clear()
        }
        */
        
        // 物品分类
        setAutoCompleteInitialValue(
            autoCompleteCategory,
            categoryList,
            currentFilter.categoryId
        )
        
        // 零件分类
        setAutoCompleteInitialValue(
            autoCompletePartCategory,
            partCategoryList,
            currentFilter.partCategoryId
        )
        
        // 所属单元
        setAutoCompleteInitialValue(
            autoCompleteUnit,
            unitList,
            currentFilter.unitId
        )
        
        // OEM编号
        inputOem.setText(currentFilter.oemNumber)
    }
    
    /**
     * 设置AutoCompleteTextView的初始值
     */
    private fun setAutoCompleteInitialValue(
        autoCompleteTextView: AutoCompleteTextView,
        items: List<Pair<String, String>>,
        selectedId: String
    ) {
        if (selectedId.isNotEmpty()) {
            val selectedItem = items.find { it.first == selectedId }
            if (selectedItem != null) {
                autoCompleteTextView.setText(selectedItem.second, false)
            } else {
                // 如果找不到对应的选项，设置为"全部"
                autoCompleteTextView.setText("全部", false)
            }
        } else {
            // 没有选择时，默认显示"全部"
            autoCompleteTextView.setText("全部", false)
        }
    }
    
    /**
     * 重置筛选条件
     */
    private fun resetFilter() {
        // 清空所有筛选条件
        currentFilter = WarehouseFilter()
        currentFilter.setTagIds(partCategoryTagId, unitTagId)
        
        // 重置UI
        // 由于适用机型选项已隐藏，不需要清空其文本
        // autoCompleteProduct.text.clear()
        autoCompleteCategory.setText("全部", false)
        autoCompletePartCategory.setText("全部", false)
        autoCompleteUnit.setText("全部", false)
        inputOem.text?.clear()
    }
    
    /**
     * 应用筛选条件
     */
    private fun applyFilter() {
        // 获取OEM编号
        currentFilter.oemNumber = inputOem.text?.toString()?.trim() ?: ""
        
        // 由于适用机型选项已隐藏，清空相关筛选条件
        currentFilter.productId = ""
        
        // 获取机型ID - 如果还没有选择机型但有输入文本，尝试匹配
        // 注释掉以下代码块，因为我们已经隐藏了适用机型选项
        /*
        val productText = autoCompleteProduct.text.toString().trim()
        if (productText.isNotEmpty() && currentFilter.productId.isEmpty()) {
            // 尝试精确匹配产品名称
            val matchedProduct = productList.find { it.second.equals(productText, ignoreCase = true) }
            if (matchedProduct != null) {
                // 找到精确匹配
                currentFilter.productId = matchedProduct.first
                productNameMap[matchedProduct.first] = matchedProduct.second
            } else {
                // 如果没有精确匹配，可以选择保留文本作为通用搜索条件
                // 在这种情况下，我们可以清空产品ID，后端可能有自己的文本搜索逻辑
                currentFilter.productId = ""
                
                // 或者提示用户选择有效的机型
                Toast.makeText(requireContext(), "请从下拉列表中选择有效的机型", Toast.LENGTH_SHORT).show()
                return // 不关闭筛选卡片，让用户重新选择
            }
        }
        */
        
        // 更新当前筛选条件的tagId
        currentFilter.setTagIds(partCategoryTagId, unitTagId)
        
        // 隐藏筛选卡片
        filterCard.visibility = View.GONE
        
        // 清除所有输入框的焦点
        autoCompleteProduct.clearFocus()
        autoCompleteCategory.clearFocus()
        autoCompletePartCategory.clearFocus()
        autoCompleteUnit.clearFocus()
        inputOem.clearFocus()
        
        // 隐藏键盘
        val imm = requireContext().getSystemService(android.content.Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
        imm.hideSoftInputFromWindow(filterCard.windowToken, 0)
        
        // 刷新数据
        refreshWarehouseItems()
    }
    
    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        adapter = WarehouseAdapter()
        // 图片全屏预览
        adapter.setOnImageClickListener { imageUrl ->
            showImagePreview(imageUrl)
        }
        binding.recyclerView.adapter = adapter
        binding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
        
        // 添加滚动监听器实现分页加载
        binding.recyclerView.addOnScrollListener(object : PaginationScrollListener(
            binding.recyclerView.layoutManager as LinearLayoutManager
        ) {
            override fun loadMoreItems() {
                isLoading = true
                currentPage++
                loadWarehouseItems()
            }
            
            override fun isLastPage(): Boolean = isLastPage
            override fun isLoading(): Boolean = isLoading
        })
    }
    
    /**
     * 设置监听器
     */
    private fun setupListeners() {
        // 设置下拉刷新
        binding.swipeRefreshLayout.setOnRefreshListener {
            refreshWarehouseItems()
        }
        
        // 底部按钮
        binding.btnApplyHistory.setOnClickListener {
            navigateToApplyHistory()
        }
        
        binding.btnReturnHistory.setOnClickListener {
            navigateToReturnHistory()
        }
        
        binding.btnApply.setOnClickListener {
            navigateToApply()
        }
        
        binding.btnReturnApply.setOnClickListener {
            navigateToReturnApply()
        }
    }
    
    /**
     * 加载筛选选项数据
     */
    private fun loadFilterOptions() {
        // 安全检查
        if (!isAdded || isFragmentDestroyed) return
        
        ApiClient.workOrderApi.getWarehouseFilterData().enqueue(object :
            Callback<ApiResponse<Map<String, Any>>> {
            override fun onResponse(
                call: Call<ApiResponse<Map<String, Any>>>,
                response: Response<ApiResponse<Map<String, Any>>>
            ) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                
                if (response.isSuccessful) {
                    // 筛选选项加载成功，后续需要实现筛选对话框
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<Map<String, Any>>>, t: Throwable) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
            }
        })
    }
    
    /**
     * 加载仓库耗材列表
     */
    private fun loadWarehouseItems() {
        isLoading = true
        // 安全检查：确保Fragment仍然附加到Activity
        if (!isAdded || isFragmentDestroyed || _binding == null) {
            Log.d("WarehouseFragment", "Fragment已销毁或未附加，跳过加载")
            return
        }
        
        binding.swipeRefreshLayout.isRefreshing = true
        
        val sharedPrefs = SharedPrefsManager(requireContext())
        val engineerId = sharedPrefs.getEngineerId()
        
        // 创建基本参数
        val baseParams = mutableMapOf<String, Any>(
            "userId" to engineerId
        )
        
        // 获取筛选参数并合并到基本参数中
        val filterParams = if (!currentFilter.isEmpty()) {
            currentFilter.toApiParams()
        } else {
            // 使用旧的筛选方式作为备选
            val oldParams = mutableMapOf<String, Any>()
            categoryId?.let { oldParams["categoryId"] = it }
            partType?.let { oldParams["categoryListId"] = it }
            unitType?.let { oldParams["unit"] = it }
            oldParams
        }
        
        // 合并所有参数
        val params = baseParams.apply { putAll(filterParams) }
        
        ApiClient.workOrderApi.getReturnableItems(params).enqueue(object :
            Callback<ApiResponse<List<WarehouseItem>>> {
            override fun onResponse(
                call: Call<ApiResponse<List<WarehouseItem>>>,
                response: Response<ApiResponse<List<WarehouseItem>>>
            ) {
                // 安全检查：确保Fragment仍然附加到Activity
                if (!isAdded || isFragmentDestroyed || _binding == null) {
                    Log.d("WarehouseFragment", "Fragment已销毁或未附加，跳过处理响应")
                    return
                }
                
                binding.swipeRefreshLayout.isRefreshing = false
                isLoading = false
                
                if (response.isSuccessful) {
                    response.body()?.data?.let { items ->
                        // 数据加载成功，显示列表
                        if (items.isNotEmpty()) {
                            // 处理API返回的数据，确保正确解析规格、图片等信息
                            val processedItems = items.map { item ->
                                // 如果API返回的数据格式与WarehouseItem不完全匹配，这里进行转换
                                val processedItem = WarehouseItem(
                                    id = item.id,
                                    itemName = item.itemName,
                                    oemNumber = item.oemNumber,
                                    // 确保availableNum正确计算，如果API返回的是总量和锁定量
                                    availableNum = item.num - (item.lockNum ?: 0),
                                    // 确保价格格式正确
                                    saleUnitPrice = try {
                                        item.saleUnitPrice?.toString()?.toDouble() ?: 0.0
                                    } catch (e: NumberFormatException) {
                                        0.0
                                    },
                                    // 从skuInfo中获取图片信息
                                    picUrl = item.skuInfo?.picUrl?.map { pic ->
                                        com.example.repairorderapp.model.warehouse.PicUrl(pic.url)
                                    } ?: emptyList(),
                                    categoryId = item.categoryId ?: "",
                                    // 构建规格信息作为分类名称显示
                                    categoryName = item.skuInfo?.saleAttrVals?.joinToString(", ") { 
                                        "${it.name}: ${it.`val`}" 
                                    } ?: "规格: 未知",
                                    num = item.num,
                                    // 添加articleCode字段
                                    articleCode = item.articleCode,
                                    // 保留原始的skuInfo和skuList信息
                                    skuInfo = item.skuInfo,
                                    skuList = item.skuList
                                )
                                processedItem
                            }
                            
                            binding.recyclerView.visibility = View.VISIBLE
                            binding.emptyView.visibility = View.GONE
                            
                            // 如果是刷新或第一页，直接替换列表
                            if (currentPage == 1) {
                                adapter.submitList(processedItems)
                            } else {
                                // 否则添加到现有列表
                                adapter.addItems(processedItems)
                            }
                            
                            // 由于这个接口没有分页，所以只加载一次
                            isLastPage = true
                        } else {
                            // 没有数据，显示空视图
                            if (currentPage == 1) {
                                binding.recyclerView.visibility = View.GONE
                                binding.emptyView.visibility = View.VISIBLE
                            }
                            isLastPage = true
                        }
                    }
                } else {
                    // 安全地使用context
                    context?.let {
                        Toast.makeText(it, "加载失败", Toast.LENGTH_SHORT).show()
                    }
                    if (currentPage == 1) {
                        binding.recyclerView.visibility = View.GONE
                        binding.emptyView.visibility = View.VISIBLE
                    }
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<List<WarehouseItem>>>, t: Throwable) {
                // 安全检查：确保Fragment仍然附加到Activity
                if (!isAdded || isFragmentDestroyed || _binding == null) {
                    Log.d("WarehouseFragment", "Fragment已销毁或未附加，跳过处理失败")
                    return
                }
                
                binding.swipeRefreshLayout.isRefreshing = false
                isLoading = false
                
                // 安全地使用context
                context?.let {
                    Toast.makeText(it, "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
                }
                
                if (currentPage == 1) {
                    binding.recyclerView.visibility = View.GONE
                    binding.emptyView.visibility = View.VISIBLE
                }
            }
        })
    }
    
    
    /**
     * 刷新仓库耗材列表
     */
    private fun refreshWarehouseItems() {
        currentPage = 1
        isLastPage = false
        
        // 记录日志以便调试筛选条件
        if (!currentFilter.isEmpty()) {
            val filterInfo = "应用筛选: " + 
                    (if (currentFilter.productId.isNotEmpty()) "机型=${currentFilter.productId}, " else "") +
                    (if (currentFilter.categoryId.isNotEmpty()) "分类=${currentFilter.categoryId}, " else "") +
                    (if (currentFilter.partCategoryId.isNotEmpty()) "零件=${currentFilter.partCategoryId}, " else "") +
                    (if (currentFilter.unitId.isNotEmpty()) "单元=${currentFilter.unitId}, " else "") +
                    (if (currentFilter.oemNumber.isNotEmpty()) "OEM=${currentFilter.oemNumber}" else "")
            
            Log.d("WarehouseFragment", filterInfo)
        }
        
        loadWarehouseItems()
    }
    
    /**
     * 加载筛选数据
     */
    private fun loadFilterData() {
        if (filterDataLoaded) return
        
        // 安全检查
        if (!isAdded || isFragmentDestroyed) return
        
        // 1. 加载物品分类数据 - 对应zhijian中的classifyList接口
        ApiClient.workOrderApi.getCategoryList("1", "9999").enqueue(object : Callback<ApiResponse<PagedData<CategoryItem>>> {
            override fun onResponse(
                call: Call<ApiResponse<PagedData<CategoryItem>>>,
                response: Response<ApiResponse<PagedData<CategoryItem>>>
            ) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                
                if (response.isSuccessful && response.body()?.code == 200) {
                    response.body()?.data?.rows?.map { 
                        Pair(it.id, it.name)
                    }?.let {
                        // 添加"全部"选项作为第一个选项
                        val allOption = Pair("", "全部")
                        categoryList = listOf(allOption) + it
                        updateFilterLoadStatus()
                    }
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<PagedData<CategoryItem>>>, t: Throwable) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                // 加载失败，可以稍后重试
            }
        })
        
        // 2. 不再预先加载机型数据，因为适用机型选项已隐藏
        // loadProductModels("", 1, 50)
        
        // 3. 加载零件分类和所属单元数据 - 对应zhijian中的getFilterData接口
        ApiClient.workOrderApi.getWarehouseFilterData().enqueue(object : Callback<ApiResponse<Map<String, Any>>> {
            override fun onResponse(
                call: Call<ApiResponse<Map<String, Any>>>,
                response: Response<ApiResponse<Map<String, Any>>>
            ) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                
                if (response.isSuccessful && response.body()?.code == 200) {
                    response.body()?.data?.let { data ->
                        // 解析标签数据（零件分类和所属单元）
                        val tagsList = data["tagList"] as? List<Map<String, Any>>
                        tagsList?.forEach { tag ->
                            when (tag["name"] as? String) {
                                "零件分类" -> {
                                    val tagId = tag["id"] as? String ?: ""
                                    val values = tag["value"] as? List<String>
                                    
                                    values?.map { 
                                        Pair(it, it)
                                    }?.let {
                                        // 添加"全部"选项作为第一个选项
                                        val allOption = Pair("", "全部")
                                        partCategoryList = listOf(allOption) + it
                                        
                                        // 保存tagId，用于API请求
                                        partCategoryTagId = tagId
                                    }
                                }
                                "所属单元" -> {
                                    val tagId = tag["id"] as? String ?: ""
                                    val values = tag["value"] as? List<String>
                                    
                                    values?.map { 
                                        Pair(it, it)
                                    }?.let {
                                        // 添加"全部"选项作为第一个选项
                                        val allOption = Pair("", "全部")
                                        unitList = listOf(allOption) + it
                                        
                                        // 保存tagId，用于API请求
                                        unitTagId = tagId
                                    }
                                }
                            }
                        }
                        
                        updateFilterLoadStatus()
                    }
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<Map<String, Any>>>, t: Throwable) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                // 加载筛选数据失败，可以稍后重试
            }
        })
    }
    
    /**
     * 加载机型数据
     */
    private fun loadProductModels(searchText: String, page: Int, pageSize: Int) {
        // 安全检查
        if (!isAdded || isFragmentDestroyed) return
        
        ApiClient.workOrderApi.getProductList(
            name = searchText,
            pageNumber = page,
            pageSize = pageSize
        ).enqueue(object : Callback<ApiResponse<PagedData<ProductItem>>> {
            override fun onResponse(
                call: Call<ApiResponse<PagedData<ProductItem>>>,
                response: Response<ApiResponse<PagedData<ProductItem>>>
            ) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                
                if (response.isSuccessful && response.body()?.code == 200) {
                    response.body()?.data?.rows?.map { 
                        Pair(it.id, it.fullName)
                    }?.let {
                        // 更新机型名称映射
                        it.forEach { pair ->
                            productNameMap[pair.first] = pair.second
                        }
                        
                        // 如果是搜索结果，替换整个列表；否则只添加新条目
                        productList = if (searchText.isNotEmpty()) {
                            it
                        } else {
                            (productList + it).distinctBy { pair -> pair.first }
                        }
                        updateFilterLoadStatus()
                    }
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<PagedData<ProductItem>>>, t: Throwable) {
                // 安全检查
                if (!isAdded || isFragmentDestroyed) return
                // 加载失败，可以稍后重试
            }
        })
    }
    
    /**
     * 更新筛选数据加载状态
     */
    private fun updateFilterLoadStatus() {
        // 当所有数据都加载完成后，标记为已加载
        if (categoryList.isNotEmpty() && (partCategoryList.isNotEmpty() || unitList.isNotEmpty())) {
            filterDataLoaded = true
        }
    }
    
    /**
     * 导航到领料记录页面
     */
    private fun navigateToApplyHistory() {
        try {
            findNavController().navigate(R.id.action_warehouseFragment_to_warehouseApplyHistoryFragment)
        } catch (e: Exception) {
            Log.e(TAG, "导航到领料记录页面失败：${e.message}", e)
            Toast.makeText(context, "页面跳转失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 导航到退料记录页面
     */
    private fun navigateToReturnHistory() {
        try {
            findNavController().navigate(R.id.warehouseReturnHistoryFragment)
        } catch (e: Exception) {
            Log.e(TAG, "导航到退料记录页面失败：${e.message}", e)
            Toast.makeText(context, "页面跳转失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 导航到申请领料页面
     */
    private fun navigateToApply() {
        try {
            findNavController().navigate(R.id.action_warehouseFragment_to_warehouseApplyFragment)
        } catch (e: Exception) {
            Log.e(TAG, "导航到申请领料页面失败：${e.message}", e)
            Toast.makeText(context, "页面跳转失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 导航到申请退料页面
     */
    private fun navigateToReturnApply() {
        try {
            findNavController().navigate(R.id.action_warehouseFragment_to_warehouseReturnApplyFragment)
        } catch (e: Exception) {
            Log.e(TAG, "导航到申请退料页面失败：${e.message}", e)
            Toast.makeText(context, "页面跳转失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    // 新增图片全屏预览
    private fun showImagePreview(imageUrl: String) {
        val dialog = ImagePreviewDialogFragment.newInstance(imageUrl)
        dialog.show(childFragmentManager, "image_preview")
    }
    
    override fun onDestroyView() {
        // 移除文本变更监听器，避免内存泄漏
        if (::autoCompleteProduct.isInitialized && autoCompleteProduct.tag is TextWatcher) {
            autoCompleteProduct.removeTextChangedListener(autoCompleteProduct.tag as TextWatcher)
        }
        
        // 取消所有协程作业
        searchCoroutineScope.cancel()
        
        // 清理资源
        if (::autoCompleteProduct.isInitialized) {
            autoCompleteProduct.setOnTouchListener(null)
            autoCompleteProduct.setOnFocusChangeListener(null)
            autoCompleteProduct.setOnItemClickListener(null)
        }
        
        super.onDestroyView()
        // 标记Fragment已被销毁
        isFragmentDestroyed = true
        _binding = null
    }
    
    /**
     * 清除所有输入框的焦点
     */
    private fun clearAllFocus() {
        if (::autoCompleteProduct.isInitialized) autoCompleteProduct.clearFocus()
        if (::autoCompleteCategory.isInitialized) autoCompleteCategory.clearFocus()
        if (::autoCompletePartCategory.isInitialized) autoCompletePartCategory.clearFocus()
        if (::autoCompleteUnit.isInitialized) autoCompleteUnit.clearFocus()
        if (::inputOem.isInitialized) inputOem.clearFocus()
    }
} 