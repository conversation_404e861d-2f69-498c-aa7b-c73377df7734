package com.example.repairorderapp.ui.orders

import android.app.Dialog
import android.content.Intent
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.HapticFeedbackConstants
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.Window
import android.widget.Button
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.example.repairorderapp.R
import com.example.repairorderapp.data.api.ApiClient
import com.example.repairorderapp.data.api.ApiResponse
import com.example.repairorderapp.data.api.WorkOrderApi
import com.example.repairorderapp.model.Engineer
import com.example.repairorderapp.model.PhotoItem
import com.example.repairorderapp.model.RepairOrder
import com.example.repairorderapp.ui.orders.EngineerListResponse
import com.example.repairorderapp.util.TimeUtil
import com.example.repairorderapp.util.setDebounceClickListener
import com.google.android.material.progressindicator.LinearProgressIndicator
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.text.SimpleDateFormat
import java.util.*
import android.graphics.Typeface
import androidx.cardview.widget.CardView
import androidx.core.widget.NestedScrollView
import com.example.repairorderapp.ui.report.RepairReportEditActivity
import android.app.ProgressDialog
import android.graphics.Rect
import android.widget.EditText
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import com.example.repairorderapp.model.WorkEvaluate
import com.example.repairorderapp.model.WorkOrderEvaluationDetail
import com.example.repairorderapp.util.await
import com.example.repairorderapp.model.EngineerWorkData
import kotlinx.coroutines.withContext

// 在文件顶部添加这些简化的数据类，用于解析工程师详情API的响应
data class EngineerDetailResponse(
    val engineerInfo: EngineerInfo? = null,
    val userManageVo: UserManageVo? = null
)

data class UserManageVo(
    val id: String = "",
    val name: String = "",
    val mobileNumber: String = ""
)

// 零件更换记录数据类
private data class ReplaceRecord(
    val createdAt: String,
    val workCode: String,
    val articleName: String,
    val articleCode: String,
    val numberOem: String,
    val num: Int,
    val partId: String,
    val blackWhite: Int,
    val color: Int
) {
    var usageDays: Int? = null
    var usagePages: Int? = null
}

class RepairOrderDetailFragmentNew : Fragment() {

    private lateinit var order: RepairOrder
    private var orderId: String? = null
    private lateinit var workOrderApi: WorkOrderApi
    private lateinit var loadingView: View
    private lateinit var contentView: View
    private lateinit var statusView: TextView
    private lateinit var progressIndicator: LinearProgressIndicator
    private lateinit var cancelButton: Button
    private lateinit var proceedButton: Button
    
    // 添加成员变量用于存储产品ID
    private var productId: String? = null
    // 新增成员变量用于存储deviceGroupId
    private var deviceGroupId: String? = null
    // 新增成员变量
    private var isFromMyOrders: Boolean = false
    // 新增：页面类型变量
    private var pageType: String? = null
    
    // 新增：保存工程师选择弹窗引用
    private var engineerSelectionDialog: AlertDialog? = null
    
    // === 新增：Activity结果监听器 ===
    private val reportEditLauncher = registerForActivityResult(
        androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == android.app.Activity.RESULT_OK) {
            // 提交报告成功，刷新工单详情
            loadWorkOrderDetail()
        }
    }
    
    private lateinit var additionalFeeEditText: EditText
    private lateinit var discountFeeEditText: EditText
    private lateinit var additionalFeeTextView: TextView
    private lateinit var discountFeeTextView: TextView
    private lateinit var saveFeeButton: Button
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_repair_order_detail, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // 初始化API
        workOrderApi = ApiClient.createService(WorkOrderApi::class.java)
        
        // 获取工单ID
        orderId = arguments?.getString("orderId")
        if (orderId == null) {
            Toast.makeText(requireContext(), "工单ID不能为空", Toast.LENGTH_SHORT).show()
            findNavController().navigateUp()
            return
        }
        
        // 初始化UI元素
        statusView = view.findViewById(R.id.tv_detail_status)
        progressIndicator = view.findViewById(R.id.progress_status)
        cancelButton = view.findViewById(R.id.btn_cancel)
        proceedButton = view.findViewById(R.id.btn_proceed)
        loadingView = view.findViewById(R.id.loading_view)
        contentView = view.findViewById(R.id.content_view)
        
        // 设置返回按钮 - 使用防抖
        view.findViewById<ImageView>(R.id.btn_back).setDebounceClickListener {
            findNavController().navigateUp()
        }
        
        // 新增：判断是否为我的工单页面进入
        pageType = arguments?.getString("type")
        isFromMyOrders = pageType == "my"
        
        // 加载工单详情
        loadWorkOrderDetail()
    }
    
    private fun loadWorkOrderDetail() {
        loadingView.visibility = View.VISIBLE
        contentView.visibility = View.GONE
        
        Log.d("WorkOrderDetail", "开始加载工单详情, ID: $orderId")
        
        workOrderApi.getWorkOrderDetail(orderId!!).enqueue(object : Callback<ApiResponse<Any>> {
            override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                Log.d("WorkOrderDetail", "工单详情API响应: ${response.code()}")
                
                loadingView.visibility = View.GONE
                
                if (response.isSuccessful) {
                    val apiResponse = response.body()
                    if (apiResponse?.code == 200 && apiResponse.data != null) {
                        // 使用后台线程解析数据，避免主线程阻塞
                        Thread {
                        try {
                            val workOrderItem = parseWorkOrderItem(apiResponse.data)
                                val mappedOrder = mapToRepairOrder(workOrderItem)
                                
                                // 将产品ID存储到成员变量中，用于分配和转派功能
                                productId = workOrderItem.productId
                                Log.d("WorkOrderDetail", "已存储产品ID: ${productId ?: "null"}")

                            // 在主线程更新UI - 安全检查Fragment状态
                            if (isAdded && activity != null) {
                                activity?.runOnUiThread {
                                    if (isAdded) {  // 再次确认Fragment仍然附加到Activity
                                        order = mappedOrder
                                        contentView.visibility = View.VISIBLE
                                        initViews(requireView())
                                    }
                                }
                            } else {
                                    Log.d("WorkOrderDetail", "Fragment已分离，跳过UI更新")

                            }
                        } catch (e: Exception) {
                                Log.e("WorkOrderDetail", "数据解析错误", e)
                                if (isAdded && activity != null) {
                                    activity?.runOnUiThread {
                                        if (isAdded) {
                                            Toast.makeText(requireContext(), "数据解析错误: ${e.message}", Toast.LENGTH_SHORT).show()
                                        }
                                    }
                                } else {
                                    Log.d("WorkOrderDetail", "Fragment已分离，跳过错误提示")
                                }
                            }
                        }.start()
                    } else if (apiResponse?.code == 401) {
                        // 处理401会话过期错误
                        if (isAdded) {
                            handleSessionExpired()
                        } else {
                            Log.d("WorkOrderDetail", "Fragment已分离，不处理会话过期")
                        }
                    } else {
                        Log.e("WorkOrderDetail", "API返回错误: ${apiResponse?.msg ?: "未知错误"}")
                        if (isAdded) {
                            Toast.makeText(requireContext(), "获取工单详情失败: ${apiResponse?.msg ?: "未知错误"}", Toast.LENGTH_SHORT).show()
                            findNavController().navigateUp()
                        }
                    }
                } else if (response.code() == 401) {
                    // 处理401会话过期错误
                    if (isAdded) {
                        handleSessionExpired()
                    } else {
                        Log.d("WorkOrderDetail", "Fragment已分离，不处理会话过期")
                    }
                } else {
                    try {
                        val errorBody = response.errorBody()?.string() ?: ""
                        Log.e("WorkOrderDetail", "获取工单详情失败: ${response.code()} - ${response.message()}, 错误详情: $errorBody")
                        
                        Toast.makeText(requireContext(), "获取工单详情失败: HTTP ${response.code()}", Toast.LENGTH_SHORT).show()
                        findNavController().navigateUp()
                    } catch (e: Exception) {
                        Log.e("WorkOrderDetail", "处理错误响应异常", e)
                        Toast.makeText(requireContext(), "获取工单详情失败", Toast.LENGTH_SHORT).show()
                    findNavController().navigateUp()
                    }
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                loadingView.visibility = View.GONE
                Log.e("WorkOrderDetail", "网络请求失败", t)
                
                // 确保Fragment仍然附加到上下文
                if (isAdded) {
                    Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
                    findNavController().navigateUp()
                }
            }
        })
    }
    
    private fun parseWorkOrderItem(data: Any): WorkOrderItem {
        // 添加详细日志帮助调试
        Log.d("WorkOrderDetail", "开始解析工单数据，类型: ${data.javaClass.name}")
        
        // 不能直接转换data为WorkOrderItem
        // 解析data（可能是Map或JsonObject）成WorkOrderItem
        if (data is Map<*, *>) {
            try {
                // 记录关键字段的可用性
                Log.d("WorkOrderDetail", "Map数据解析，可用字段: " + 
                      data.keys.joinToString(", "))
                
                if (data.containsKey("id")) {
                    Log.d("WorkOrderDetail", "工单ID: ${data["id"]}")
                }
                
                // 从Map中提取数据 - 主要字段
                val id = data["id"]?.toString() ?: ""
                val code = data["code"]?.toString() ?: ""
                
                // 处理客户信息
                val customerId = data["customerId"]?.toString() ?: ""
                // 优先使用customer.name，如果customer为空则使用customerName
                val customerName = if (data.containsKey("customer") && data["customer"] != null) {
                    (data["customer"] as? Map<*, *>)?.get("name")?.toString() ?: 
                        data["customerName"]?.toString() ?: ""
                } else {
                    data["customerName"]?.toString() ?: ""
                }
                
                // 详细输出客户相关的所有字段
                Log.d("WorkOrderDetail", "客户信息详细数据 - customerId: $customerId, customerName: $customerName")
                Log.d("WorkOrderDetail", "customer对象: ${data["customer"]}")
                
                // 获取报修用户相关的所有可能字段
                val reporterKeywords = listOf("reporter", "reporterName", "reporterPhone", "reporterMobile", "reporterTel", "contactPerson", "contactPhone", "customerStaff")
                val reporterData = mutableMapOf<String, Any?>()
                reporterKeywords.forEach { key ->
                    if (data.containsKey(key)) {
                        reporterData[key] = data[key]
                    }
                }
                Log.d("WorkOrderDetail", "所有报修用户相关字段: $reporterData")
                
                // 获取地址相关的所有可能字段
                val addressKeywords = listOf("address", "customerAddress", "location", "serviceAddress", "detailAddress", "customerRegion")
                val addressData = mutableMapOf<String, Any?>()
                addressKeywords.forEach { key ->
                    if (data.containsKey(key)) {
                        addressData[key] = data[key]
                    }
                }
                Log.d("WorkOrderDetail", "所有地址相关字段: $addressData")
                
                // 设备信息
                val deviceNumber = data["deviceNumber"]?.toString() ?: 
                                   data["deviceNo"]?.toString() ?: ""
                                   
                val brand = data["brand"]?.toString() ?: ""
                val machine = data["machine"]?.toString() ?: 
                              data["deviceName"]?.toString() ?: ""
                              
                // 解析地址 - 提前处理地址以便其他地方可以使用
                var address = ""
                
                // 优先使用address字段
                if (data.containsKey("address") && data["address"] != null) {
                    address = data["address"].toString()
                    Log.d("WorkOrderDetail", "从顶级address字段获取地址: $address")
                }
                
                // 如果address为空，尝试customerRegion + address组合
                if (address.isEmpty() && data.containsKey("customerRegion")) {
                    val region = data["customerRegion"]?.toString() ?: ""
                    if (region.isNotEmpty()) {
                        address = region
                        // 如果有详细地址，添加上
                        val detailAddr = data["detailAddress"]?.toString() ?: ""
                        if (detailAddr.isNotEmpty()) {
                            address += detailAddr
                        }
                        Log.d("WorkOrderDetail", "从customerRegion+detailAddress组合获取地址: $address")
                    }
                }
                
                // 如果仍然为空，尝试其他可能的字段
                if (address.isEmpty()) {
                    address = data["customerAddress"]?.toString() ?: 
                             data["serviceAddress"]?.toString() ?: ""
                    Log.d("WorkOrderDetail", "从备选字段获取地址: $address")
                }
                
                // 如果customer对象存在并包含address，优先使用customer.address
                if (address.isEmpty() && data["customer"] is Map<*, *>) {
                    val customerMap = data["customer"] as Map<*, *>
                    if (customerMap.containsKey("address")) {
                        val customerAddress = customerMap["address"]?.toString()
                        if (!customerAddress.isNullOrEmpty()) {
                            address = customerAddress
                            Log.d("WorkOrderDetail", "使用customer.address: $address")
                        }
                    }
                }
                
                Log.d("WorkOrderDetail", "最终解析到的地址: $address")
                               
                // 处理经纬度
                var latitude = 0.0
                var longitude = 0.0
                
                // 尝试获取经纬度 - 先检查location对象
                if (data["location"] is Map<*, *>) {
                    val locationMap = data["location"] as Map<*, *>
                    latitude = locationMap["latitude"]?.toString()?.toDoubleOrNull() ?: 0.0
                    longitude = locationMap["longitude"]?.toString()?.toDoubleOrNull() ?: 0.0
                    Log.d("WorkOrderDetail", "从location对象获取经纬度: lat=$latitude, lng=$longitude")
                } else {
                    // 如果没有location对象，尝试直接获取latitude和longitude字段
                    latitude = data["latitude"]?.toString()?.toDoubleOrNull() ?: 0.0
                    longitude = data["longitude"]?.toString()?.toDoubleOrNull() ?: 0.0
                    Log.d("WorkOrderDetail", "从顶级字段获取经纬度: lat=$latitude, lng=$longitude")
                }
                
                // 如果customer对象中有location，尝试从那里获取
                if ((latitude == 0.0 || longitude == 0.0) && data["customer"] is Map<*, *>) {
                    val customerMap = data["customer"] as Map<*, *>
                    if (customerMap["location"] is Map<*, *>) {
                        val locationMap = customerMap["location"] as Map<*, *>
                        latitude = locationMap["latitude"]?.toString()?.toDoubleOrNull() ?: latitude
                        longitude = locationMap["longitude"]?.toString()?.toDoubleOrNull() ?: longitude
                        Log.d("WorkOrderDetail", "从customer.location获取经纬度: lat=$latitude, lng=$longitude")
                    } else {
                        // 直接从customer对象获取
                        val custLat = customerMap["latitude"]?.toString()?.toDoubleOrNull()
                        val custLng = customerMap["longitude"]?.toString()?.toDoubleOrNull()
                        if (custLat != null && custLng != null) {
                            latitude = custLat
                            longitude = custLng
                            Log.d("WorkOrderDetail", "从customer直接字段获取经纬度: lat=$latitude, lng=$longitude")
                        }
                    }
                }
                
                // 故障描述
                val excDesc = data["excDesc"]?.toString() ?: 
                              data["faultDesc"]?.toString() ?: ""
                
                // 故障照片
                val excPics = if (data["excPics"] is List<*>) {
                    data["excPics"] as List<Map<String, Any>>
                } else {
                    emptyList()
                }
                
                // 记录故障照片信息
                if (excPics.isNotEmpty()) {
                    Log.d("WorkOrderDetail", "检测到故障照片: ${excPics.size}张")
                    excPics.forEachIndexed { index, pic ->
                        Log.d("WorkOrderDetail", "照片 $index: ${pic["url"]}")
                    }
                } else {
                    Log.d("WorkOrderDetail", "没有故障照片")
                }
                
                // 时间相关
                val createdAt = data["createdAt"]?.toString() ?: 
                                data["createTime"]?.toString() ?: ""
                val beginTime = data["beginTime"]?.toString() ?: 
                                data["startTime"]?.toString()
                val endTime = data["endTime"]?.toString() ?: 
                              data["completedAt"]?.toString()
                              
                // 添加更多时间信息
                val expectArriveTime = data["expectArriveTime"]?.toString() ?: 
                                      data["expectTime"]?.toString() ?: ""
                val prospectArriveTime = data["prospectArriveTime"]?.toString() ?: 
                                        data["prospectTime"]?.toString() ?: ""
                val arriveTimeStr = data["arriveTime"]?.toString() ?:
                                data["arrivalTime"]?.toString() ?: ""
                val arriveTime = if (arriveTimeStr.isNotEmpty()) parseDateTime(arriveTimeStr) else null
                val finishTimeStr = data["finishTime"]?.toString() ?:
                                data["closeTime"]?.toString() ?: ""
                val finishTime = if (finishTimeStr.isNotEmpty()) parseDateTime(finishTimeStr) else null
                
                // 添加工单状态时间字段解析
                val orderReceiveTime = data["orderReceiveTime"]?.toString()
                val departureTime = data["departureTime"]?.toString()
                val actualArriveTime = data["actualArriveTime"]?.toString()
                val sendReportTime = data["sendReportTime"]?.toString()
                val confirmReportTime = data["confirmReportTime"]?.toString()
                val completedAt = data["completedAt"]?.toString()
                
                val productId = data["productId"]?.toString()
                
                // 添加productInfo字段的提取
                val productInfo = data["productInfo"]?.toString()
                
                // 解析费用相关数据
                val visitPay = data["visitPay"]?.toString()?.toDoubleOrNull() ?: 
                               data["visitFee"]?.toString()?.toDoubleOrNull()
                val longWayVisitPay = data["longWayVisitPay"]?.toString()?.toDoubleOrNull() ?: 
                                     data["remoteFee"]?.toString()?.toDoubleOrNull()
                val repairPay = data["repairPay"]?.toString()?.toDoubleOrNull() ?: 
                                data["diagnosisFee"]?.toString()?.toDoubleOrNull()
                val itemPay = data["itemPay"]?.toString()?.toDoubleOrNull() ?: 
                              data["materialsFee"]?.toString()?.toDoubleOrNull()
                val actualReplacePay = data["actualReplacePay"]?.toString()?.toDoubleOrNull() ?: 
                                      data["partsFee"]?.toString()?.toDoubleOrNull()
                val engineerAdditionalPay = data["engineerAdditionalPay"]?.toString()?.toDoubleOrNull() ?: 
                                          data["engExtraFee"]?.toString()?.toDoubleOrNull()
                val derateAmount = data["derateAmount"]?.toString()?.toDoubleOrNull() ?: 
                                  data["engDiscount"]?.toString()?.toDoubleOrNull()
                val discountAmount = data["discountAmount"]?.toString()?.toDoubleOrNull() ?: 
                                    data["memberDiscount"]?.toString()?.toDoubleOrNull()
                val additionalPay = data["additionalPay"]?.toString()?.toDoubleOrNull() ?: 
                                   data["custExtraFee"]?.toString()?.toDoubleOrNull()
                val totalPay = data["totalAmount"]?.toString()?.toDoubleOrNull() ?: 
                              data["totalPayable"]?.toString()?.toDoubleOrNull()
                val actualPay = data["totalPay"]?.toString()?.toDoubleOrNull() ?: 
                               data["totalPaid"]?.toString()?.toDoubleOrNull()
                
                // 解析嵌套对象
                val customer = parseCustomer(data["customer"], address)
                
                // 工程师信息 - 兼容不同API格式
                var engineerId: EngineerInfo? = null
                if (data.containsKey("engineerId")) {
                    if (data["engineerId"] is Map<*, *>) {
                        engineerId = parseEngineer(data["engineerId"])
                    } else {
                        val engId = data["engineerId"]?.toString() ?: ""
                        val engName = data["engineerName"]?.toString() ?: ""
                        if (engId.isNotEmpty()) {
                            engineerId = EngineerInfo(engId, engName)
                        }
                    }
                }
                
                // 设备组、服务类型和状态
                var deviceGroup = parseDictItem(data["deviceGroup"])
                
                // 如果设备组为空或label为空，尝试从customerDeviceGroup中获取
                if (deviceGroup == null || deviceGroup.label.isEmpty()) {
                    if (data["customerDeviceGroup"] is Map<*, *>) {
                        val customerDeviceGroup = data["customerDeviceGroup"] as Map<*, *>
                        if (customerDeviceGroup["deviceGroup"] is Map<*, *>) {
                            deviceGroup = parseDictItem(customerDeviceGroup["deviceGroup"])
                        }
                    }
                }
                
                // 如果仍然为空，使用deviceGroupId和deviceGroupName
                if (deviceGroup == null || deviceGroup.label.isEmpty()) {
                    deviceGroup = DictItem(
                        data["deviceGroupId"]?.toString() ?: "", 
                        data["deviceGroupName"]?.toString() ?: ""
                    )
                }
                
                // 新增：保存deviceGroupId
                deviceGroupId = data["deviceGroupId"]?.toString() ?: ""
                
                // 如果仍然没有标签，尝试从deviceGroup字符串获取
                if (deviceGroup.label.isEmpty() && data["deviceGroup"] is String) {
                    deviceGroup = DictItem("deviceGroup", data["deviceGroup"].toString())
                }
                
                // 添加设备组解析日志                  
                Log.d("WorkOrderDetail", "解析设备组信息：原始数据=" + data["deviceGroup"] + 
                      ", customerDeviceGroup=" + data["customerDeviceGroup"] +
                      ", deviceGroupId=" + data["deviceGroupId"] + 
                      ", deviceGroupName=" + data["deviceGroupName"] + 
                      ", 解析结果label=" + deviceGroup.label + 
                      ", value=" + deviceGroup.value)
                                          
                val serType = parseDictItem(data["serType"]) ?: 
                              DictItem(data["serTypeId"]?.toString() ?: "", 
                                      data["serTypeName"]?.toString() ?: "")
                                      
                val status = parseDictItem(data["status"]) ?: 
                             DictItem(data["statusId"]?.toString() ?: "", 
                                     data["statusName"]?.toString() ?: "")
                
                Log.d("WorkOrderDetail", "数据解析成功: ID=$id, 客户=$customerName, 机型=$productInfo, 工程师=${engineerId?.name ?: "未分配"}, 设备组=${deviceGroup.label}")
                
                // 添加提取报修人信息 - 现在优先检查customerStaff字段
                var reporter = ""
                var reporterMobile = ""
                
                // 从customerStaff获取报修人信息
                if (data["customerStaff"] is Map<*, *>) {
                    val staffMap = data["customerStaff"] as Map<*, *>
                    reporter = staffMap["name"]?.toString() ?: ""
                    reporterMobile = staffMap["tel"]?.toString() ?: ""
                    Log.d("WorkOrderDetail", "从customerStaff获取报修人信息: 姓名=$reporter, 电话=$reporterMobile")
                }
                
                // 如果未能从customerStaff获取，尝试其他字段
                if (reporter.isEmpty()) {
                    reporter = data["reporter"]?.toString() ?: 
                               data["reporterName"]?.toString() ?: 
                               data["contactPerson"]?.toString() ?: ""
                }
                
                if (reporterMobile.isEmpty()) {
                    reporterMobile = data["reporterMobile"]?.toString() ?: 
                                     data["reporterPhone"]?.toString() ?: 
                                     data["reporterTel"]?.toString() ?: 
                                     data["contactPhone"]?.toString() ?: 
                                     data["legalPersonTel"]?.toString() ?: ""
                }
                
                // 添加详细日志 - 报修人信息
                Log.d("WorkOrderDetail", "最终报修人信息: 姓名=${reporter}, 电话=${reporterMobile}")
                
                return WorkOrderItem(
                    id = id,
                    code = code,
                    customerId = customerId,
                    customerName = customerName,
                    customer = customer,
                    engineerId = engineerId,
                    deviceNumber = deviceNumber,
                    brand = brand,
                    machine = machine,
                    deviceGroup = deviceGroup,
                    deviceGroupId = deviceGroupId,
                    deviceGroupImg = data["deviceGroupImg"]?.toString(),  // 添加设备照片字段
                    latitude = latitude,
                    longitude = longitude,
                    serType = serType,
                    status = status,
                    excDesc = excDesc,
                    createdAt = createdAt,
                    beginTime = beginTime,
                    endTime = endTime,
                    productId = productId,
                    productInfo = productInfo,
                    excPics = excPics,  // 添加故障照片
                    // 时间信息
                    expectArriveTime = expectArriveTime,
                    prospectArriveTime = prospectArriveTime,
                    arriveTime = arriveTime,
                    waitConfirmTime = if (status.value == "wait_confirmed_report") parseDateTime(createdAt) else null,
                    finishTime = finishTime,
                    // 费用信息
                    visitPay = visitPay,
                    longWayVisitPay = longWayVisitPay,
                    repairPay = repairPay,
                    itemPay = itemPay,
                    actualReplacePay = actualReplacePay,
                    engineerAdditionalPay = engineerAdditionalPay,
                    derateAmount = derateAmount,
                    discountAmount = discountAmount,
                    additionalPay = additionalPay,
                    totalPay = totalPay,
                    actualPay = actualPay,
                    // 报修人信息
                    reporter = reporter,
                    reporterMobile = reporterMobile,
                    // 工单状态时间字段
                    orderReceiveTime = orderReceiveTime,
                    departureTime = departureTime,
                    actualArriveTime = actualArriveTime,
                    sendReportTime = sendReportTime,
                    confirmReportTime = confirmReportTime,
                    completedAt = completedAt
                )
            } catch (e: Exception) {
                Log.e("WorkOrderDetail", "解析异常: ${e.message}", e)
                throw Exception("数据格式解析错误: ${e.message}")
            }
        } else {
            // 处理可能的其他数据类型，如JSONObject
            try {
                val mapData = convertToMap(data)
                return parseWorkOrderItem(mapData)
            } catch (e: Exception) {
                Log.e("WorkOrderDetail", "转换数据失败: ${e.message}", e)
                throw Exception("不支持的数据格式: ${data.javaClass.name}")
            }
        }
    }
    
    // 将任意类型的数据对象尝试转换为Map
    private fun convertToMap(data: Any): Map<String, Any?> {
        Log.d("WorkOrderDetail", "尝试将 ${data.javaClass.name} 转换为Map")
        
        // 如果是JSON字符串，先解析为JSONObject
        if (data is String) {
            try {
                val jsonObj = org.json.JSONObject(data)
                return jsonObjectToMap(jsonObj)
            } catch (e: Exception) {
                Log.e("WorkOrderDetail", "JSON解析失败", e)
                return emptyMap()
            }
        }
        
        // 如果是JSONObject，转换为Map
        if (data::class.java.name.contains("JSONObject")) {
            try {
                val method = data.javaClass.getMethod("keys")
                val keysIterator = method.invoke(data) as Iterator<*>
                val result = mutableMapOf<String, Any?>()
                
                while (keysIterator.hasNext()) {
                    val key = keysIterator.next() as String
                    val getValue = data.javaClass.getMethod("get", String::class.java)
                    val value = getValue.invoke(data, key)
                    result[key] = value
                }
                
                return result
            } catch (e: Exception) {
                Log.e("WorkOrderDetail", "JSONObject转换失败", e)
                return emptyMap()
            }
        }
        
        // 无法转换时返回空Map
        return emptyMap()
    }
    
    private fun jsonObjectToMap(jsonObject: org.json.JSONObject): Map<String, Any?> {
        val result = mutableMapOf<String, Any?>()
        val keys = jsonObject.keys()
        
        while (keys.hasNext()) {
            val key = keys.next()
            val value = jsonObject.get(key)
            
            // 处理嵌套的JSONObject
            if (value is org.json.JSONObject) {
                result[key] = jsonObjectToMap(value)
            } 
            // 处理JSONArray
            else if (value is org.json.JSONArray) {
                result[key] = jsonArrayToList(value)
            } 
            // 处理原始值
            else {
                result[key] = value
            }
        }
        
        return result
    }
    
    private fun jsonArrayToList(jsonArray: org.json.JSONArray): List<Any?> {
        val result = mutableListOf<Any?>()
        
        for (i in 0 until jsonArray.length()) {
            val value = jsonArray.get(i)
            
            // 处理嵌套的JSONObject
            if (value is org.json.JSONObject) {
                result.add(jsonObjectToMap(value))
            } 
            // 处理嵌套的JSONArray
            else if (value is org.json.JSONArray) {
                result.add(jsonArrayToList(value))
            } 
            // 处理原始值
            else {
                result.add(value)
            }
        }
        
        return result
    }
    
    private fun parseCustomer(data: Any?, fallbackAddress: String = ""): Customer? {
        if (data == null) {
            Log.d("WorkOrderDetail", "Customer对象为null，使用备用地址: $fallbackAddress")
            if (fallbackAddress.isNotEmpty()) {
                return Customer("", "", fallbackAddress)
            }
            return null
        }
        
        if (data is Map<*, *>) {
            val id = data["id"]?.toString() ?: ""
            val name = data["name"]?.toString() ?: ""
            var address = data["address"]?.toString() ?: fallbackAddress
            
            // 记录Customer解析结果
            Log.d("WorkOrderDetail", "解析Customer对象 - id: $id, name: $name, address: $address")
            
            return Customer(id, name, address)
        }
        
        Log.d("WorkOrderDetail", "Customer对象类型不是Map: ${data.javaClass.name}")
        return null
    }
    
    private fun parseEngineer(data: Any?): EngineerInfo? {
        if (data == null) return null
        if (data is Map<*, *>) {
            val id = data["id"]?.toString() ?: ""
            val name = data["name"]?.toString() ?: ""
            return EngineerInfo(id, name)
        }
        return null
    }
    
    private fun parseDictItem(data: Any?): DictItem? {
        if (data == null) return null
        if (data is Map<*, *>) {
            val value = data["value"]?.toString() ?: ""
            val label = data["label"]?.toString() ?: ""
            return DictItem(value, label)
        }
        return null
    }
    
    private fun mapToRepairOrder(item: WorkOrderItem): RepairOrder {
        // 记录设备组信息的处理
        val deviceGroupInfo = item.deviceGroup?.label ?: ""
        Log.d("WorkOrderDetail", "映射到RepairOrder - 设备组原始信息: ${item.deviceGroup?.label}, 值: ${item.deviceGroup?.value}")
        
        // 映射故障照片
        val faultPhotos = item.excPics?.mapNotNull { pic ->
            try {
                val id = pic["id"]?.toString() ?: ""
                val url = pic["url"]?.toString() ?: ""
                val key = pic["key"]?.toString() ?: ""
                if (url.isNotEmpty()) {
                    PhotoItem(id, url, key)
                } else null
            } catch (e: Exception) {
                Log.e("WorkOrderDetail", "照片映射错误: ${e.message}")
                null
            }
        } ?: emptyList()
        
        // 记录故障照片映射结果
        Log.d("WorkOrderDetail", "故障照片映射结果: ${faultPhotos.size}张")
        
        // 3. 优先用item.deviceGroupId，其次用item.deviceGroup?.value
        val deviceGroupId = item.deviceGroupId ?: ""
        
        return RepairOrder(
            id = item.id,
            code = item.code ?: "",
            customerId = item.customerId ?: "",
            customerName = item.customerName ?: "",
            address = item.customer?.address ?: "",
            latitude = item.latitude ?: 0.0,
            longitude = item.longitude ?: 0.0,
            engineerId = item.engineerId?.id,
            engineerName = item.engineerId?.name,
            status = item.status?.label ?: mapStatusLabel(item.status?.value),
            statusValue = item.status?.value ?: "",
            createTime = parseDateTime(item.createdAt),
            startTime = if (item.beginTime != null) parseDateTime(item.beginTime) else null,
            completedAt = if (item.endTime != null) parseDateTime(item.endTime) else null,
            arriveTime = item.arriveTime,
            waitConfirmTime = item.waitConfirmTime,
            // 工单状态时间字段
            receiveTime = if (item.orderReceiveTime != null) parseDateTime(item.orderReceiveTime) else null,
            departureTime = if (item.departureTime != null) parseDateTime(item.departureTime) else null,
            actualArriveTime = if (item.actualArriveTime != null) parseDateTime(item.actualArriveTime) else null,
            sendReportTime = if (item.sendReportTime != null) parseDateTime(item.sendReportTime) else null,
            confirmReportTime = if (item.confirmReportTime != null) parseDateTime(item.confirmReportTime) else null,
            finishTime = item.finishTime,
            machineNumber = item.deviceNumber ?: "",
            machineModel = when {
                !item.productInfo.isNullOrEmpty() -> item.productInfo
                !item.brand.isNullOrEmpty() && !item.machine.isNullOrEmpty() -> "${item.brand}/${item.machine}"
                !item.brand.isNullOrEmpty() -> item.brand
                !item.machine.isNullOrEmpty() -> item.machine
                else -> ""
            },
            deviceGroup = deviceGroupInfo,
            deviceGroupId = deviceGroupId,
            serviceType = item.serType?.label ?: "",
            colorType = "彩色机", // 默认值改为彩色机
            problemDesc = item.excDesc ?: "",
            deviceGroupImg = item.deviceGroupImg,  // 添加设备照片映射
            faultPhotos = faultPhotos,  // 添加故障照片
            bwCounter = 0, // 计数器值仍需API提供，这里保持默认0
            colorCounter = 0,
            fifthColorCounter = 0,
            // 映射费用信息
            visitPay = item.visitPay ?: 0.0,
            longWayVisitPay = item.longWayVisitPay ?: 0.0,
            repairPay = item.repairPay ?: 0.0,
            itemPay = item.itemPay ?: 0.0,
            actualReplacePay = item.actualReplacePay ?: 0.0,
            engineerAdditionalPay = item.engineerAdditionalPay ?: 0.0,
            derateAmount = item.derateAmount ?: 0.0,
            discountAmount = item.discountAmount ?: 0.0,
            additionalPay = item.additionalPay ?: 0.0,
            totalPay = item.totalPay ?: 0.0,
            actualPay = item.actualPay ?: 0.0,
            // 映射时间信息
            expectArriveTime = item.expectArriveTime ?: "",
            prospectArriveTime = item.prospectArriveTime ?: "",
            // 添加报修人信息
            reporter = item.reporter ?: "",
            reporterMobile = item.reporterMobile ?: ""
        )
    }
    
    private fun mapStatusLabel(statusValue: String?): String {
        return when (statusValue) {
            "pending_orders" -> "待接单"
            "engineer_receive" -> "已分配"
            "engineer_departure" -> "路途中"
            "engineer_arrive" -> "维修中"
            "wait_confirmed_report" -> "等待确认"
            "completed" -> "已完成"
            "close" -> "已关闭"
            else -> "待处理"
        }
    }
    
    private fun parseDateTime(dateTimeString: String?): Date {
        if (dateTimeString.isNullOrEmpty()) {
            return Calendar.getInstance().time
        }
        
        return try {
            // 支持多种日期格式
            val formats = arrayOf(
                "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
                "yyyy-MM-dd'T'HH:mm:ss'Z'",
                "yyyy-MM-dd'T'HH:mm:ss.SSSXXX",
                "yyyy-MM-dd'T'HH:mm:ssXXX",
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd HH:mm",
                "yyyy/MM/dd HH:mm:ss",
                "yyyy/MM/dd HH:mm",
                "yyyy年MM月dd日 HH:mm:ss",
                "yyyy年MM月dd日 HH:mm"
            )
            
            // 尝试每种格式
            for (format in formats) {
                try {
                    val sdf = SimpleDateFormat(format, Locale.getDefault())
                    // 根据不同格式设置不同的时区
                    if (format.contains("'Z'")) {
                        // ISO 8601 UTC时间格式（带Z结尾），需要设置为UTC时区
                        sdf.timeZone = TimeZone.getTimeZone("UTC")
                    } else if (format.contains("XXX")) {
                        // ISO 8601时间格式（带时区偏移，如+08:00），使用默认时区处理
                        // 注：SimpleDateFormat可以自动处理XXX格式的时区
                    } else {
                        // 本地时间格式，设置为设备所在时区
                        sdf.timeZone = TimeZone.getDefault()
                    }
                    
                    val parsedDate = sdf.parse(dateTimeString)
                    if (parsedDate != null) {
                        // 如果是UTC时间格式，转换为本地时区显示
                        if (format.contains("'Z'")) {
                            // 记录转换日志
                            Log.d("TimeZone", "从UTC转换时间：${dateTimeString} => ${formatDateTimeIso(parsedDate)}")
                        }
                        return parsedDate
                    }
                } catch (e: Exception) {
                    // 继续尝试下一个格式
                }
            }
            
            // 如果所有格式都失败，尝试处理时间戳
            val timestamp = dateTimeString.toLongOrNull()
            if (timestamp != null) {
                return Date(timestamp)
            }
            
            // 最后返回当前时间
            Log.w("WorkOrderDetail", "无法解析日期时间: $dateTimeString")
            Calendar.getInstance().time
        } catch (e: Exception) {
            Log.e("WorkOrderDetail", "日期时间解析异常: ${e.message}", e)
            Calendar.getInstance().time
        }
    }
    
    // 用于显示日期时间的格式化方法
    private fun formatDateTimeIso(date: Date): String {
        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
        sdf.timeZone = TimeZone.getDefault() // 确保使用本地时区
        return sdf.format(date)
    }
    
    private fun initViews(view: View) {
        // 设置基本信息
        setupBasicInfo(view)
        
        // 设置工程师信息
        setupEngineerInfo(view)
        
        // 设置设备信息
        setupDeviceInfo(view)
        
        // 设置快捷操作按钮（如导航按钮）
        setupNavigationButton(view)
        
        // 设置报修人联系按钮
        setupContactButton(view)
        
        // 设置维修报告按钮
        setupReportButton(view)
        
        // 修正：根据页面类型和工单状态决定是否显示底部操作按钮
        val buttonLayout = view.findViewById<LinearLayout>(R.id.button_layout)
        val nestedScrollView = view.findViewById<NestedScrollView>(R.id.nested_scroll_view)
        val contentLayout = nestedScrollView?.getChildAt(0) as? LinearLayout
        
        // 判断是否需要显示操作按钮
        val shouldShowButtons = when {
            // 从我的工单页面进入，显示按钮
            isFromMyOrders -> true
            // 从待接工单页面进入，且工单状态为待接单时，显示接单按钮
            pageType == "pending" && order.statusValue == "pending_orders" -> true
            // 其他情况不显示按钮
            else -> false
        }
        
        if (shouldShowButtons) {
            buttonLayout?.visibility = View.VISIBLE
            setupProceedAndCancelButton(view)
            // 动态调整内容区padding，避免被按钮遮挡
            adjustContentBottomPadding(contentLayout, buttonLayout)
        } else {
            buttonLayout?.visibility = View.GONE
            // 恢复默认padding
            resetContentBottomPadding(contentLayout)
        }
        
        // 初始化费用相关视图
        setupFeeViews(view)
        
        // 加载并显示客户评价
        loadCustomerEvaluation(view)
    }
    
    /**
     * 初始化费用相关视图
     */
    private fun setupFeeViews(view: View) {
        // 获取视图引用
        additionalFeeEditText = view.findViewById(R.id.et_additional_fee)
        discountFeeEditText = view.findViewById(R.id.et_discount_fee)
        additionalFeeTextView = view.findViewById(R.id.tv_additional_fee)
        discountFeeTextView = view.findViewById(R.id.tv_discount_fee)
        saveFeeButton = view.findViewById(R.id.btn_save_fee)
        
        // 设置初始值 - 使用与其他费用项一致的格式
        additionalFeeTextView.text = formatFee(order.engineerAdditionalPay)
        discountFeeTextView.text = formatFee(-order.derateAmount) // 使用负数表示减免
        
        // 根据工单状态和来源页面决定是否可编辑费用
        // 只有当工单状态为"工程师到店"且从"我的工单"页面进入时才允许编辑
        val isEditable = order.statusValue == "engineer_arrive" && isFromMyOrders
        
        if (isEditable) {
            // 显示输入框和保存按钮
            additionalFeeEditText.visibility = View.VISIBLE
            discountFeeEditText.visibility = View.VISIBLE
            additionalFeeTextView.visibility = View.GONE
            discountFeeTextView.visibility = View.GONE
            saveFeeButton.visibility = View.VISIBLE
            
            // 设置输入框初始值
            additionalFeeEditText.setText(if (order.engineerAdditionalPay > 0) order.engineerAdditionalPay.toString() else "")
            discountFeeEditText.setText(if (order.derateAmount > 0) order.derateAmount.toString() else "")
            
            // 添加保存按钮点击事件 - 使用1500ms防抖
            saveFeeButton.setDebounceClickListener(1500) {
                saveFeeChanges()
            }
        } else {
            // 显示文本，隐藏输入框和保存按钮
            additionalFeeEditText.visibility = View.GONE
            discountFeeEditText.visibility = View.GONE
            additionalFeeTextView.visibility = View.VISIBLE
            discountFeeTextView.visibility = View.VISIBLE
            saveFeeButton.visibility = View.GONE
        }
    }
    
    /**
     * 保存费用修改
     */
    private fun saveFeeChanges() {
        try {
            // 获取输入值
            val additionalFeeText = additionalFeeEditText.text.toString().trim()
            val discountFeeText = discountFeeEditText.text.toString().trim()
            
            // 检查是否两个字段都填写了
            if (additionalFeeText.isNotEmpty() && discountFeeText.isNotEmpty()) {
                Toast.makeText(
                    requireContext(), 
                    "不能同时设置追加费用和减免费用，请只填写一项", 
                    Toast.LENGTH_SHORT
                ).show()
                return
            }
            
            // 转换为数字
            val additionalFee = if (additionalFeeText.isEmpty()) 0.0 else additionalFeeText.toDouble()
            val discountFee = if (discountFeeText.isEmpty()) 0.0 else discountFeeText.toDouble()
            
            // 验证数据
            if (additionalFee < 0 || discountFee < 0) {
                Toast.makeText(requireContext(), "金额不能为负数", Toast.LENGTH_SHORT).show()
                return
            }
            
            // 显示加载提示
            val loadingDialog = ProgressDialog(requireContext())
            loadingDialog.setMessage("保存中...")
            loadingDialog.setCancelable(false)
            loadingDialog.show()
            
            // 保存追加费用
            if (additionalFee > 0) {
                // 如果填写了追加费用，则保存追加费用并清空减免费用
                val params = HashMap<String, String>()
                params["id"] = orderId ?: ""
                params["engineerAdditionalPay"] = additionalFee.toString()
                params["derateAmount"] = "0"  // 清空减免费用
                
                saveWithParams(params, loadingDialog, "追加费用")
            } 
            // 保存减免费用
            else if (discountFee > 0) {
                // 如果填写了减免费用，则保存减免费用并清空追加费用
                val params = HashMap<String, String>()
                params["id"] = orderId ?: ""
                params["derateAmount"] = discountFee.toString()
                params["engineerAdditionalPay"] = "0"  // 清空追加费用
                
                saveWithParams(params, loadingDialog, "减免费用")
            } 
            // 都未填写
            else {
                // 如果都未填写，则清空双方费用
                val params = HashMap<String, String>()
                params["id"] = orderId ?: ""
                params["derateAmount"] = "0"
                params["engineerAdditionalPay"] = "0"
                
                saveWithParams(params, loadingDialog, "费用清空")
            }
        } catch (e: Exception) {
            Log.e("WorkOrderDetail", "保存费用出错: ${e.message}", e)
            Toast.makeText(requireContext(), "保存费用出错: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 使用指定参数保存费用
     */
    private fun saveWithParams(params: HashMap<String, String>, loadingDialog: ProgressDialog, operationType: String) {
        workOrderApi.discountWorkOrder(params).enqueue(object : Callback<ApiResponse<Any>> {
            override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                loadingDialog.dismiss()
                if (response.isSuccessful && response.body()?.code == 200) {
                    // 加载最新数据
                    loadWorkOrderDetail()
                    Toast.makeText(requireContext(), "${operationType}保存成功", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(requireContext(), "${operationType}保存失败: ${response.body()?.msg ?: "未知错误"}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                loadingDialog.dismiss()
                Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    
    // 新增：根据工单状态设置底部操作按钮
    private fun setupProceedAndCancelButton(view: View) {
        val proceedButton = view.findViewById<Button>(R.id.btn_proceed)
        val cancelButton = view.findViewById<Button>(R.id.btn_cancel)
        
        // 待确认维修报告状态下，显示"撤回维修报告"按钮
        if (order.statusValue == "wait_confirmed_report") {
            proceedButton.visibility = View.VISIBLE
            proceedButton.text = "撤回维修报告"
            proceedButton.setDebounceClickListener(1500) {
                showRecallReportConfirmDialog()
            }
            cancelButton.visibility = View.GONE
            return
        }
        
        // 客户已确认、已完成、已关闭等状态下隐藏所有操作按钮
        if (order.statusValue == "completed" ||
            order.statusValue == "close") {
            proceedButton.visibility = View.GONE
            cancelButton.visibility = View.GONE
            return
        }
        // engineer_arrive状态下显示"填写报告"
        if (order.statusValue == "engineer_arrive") {
            proceedButton.visibility = View.VISIBLE
            proceedButton.text = "填写报告"
            proceedButton.setDebounceClickListener(1500) {
                fillRepairReport(order.id)
            }
            cancelButton.visibility = View.GONE
            return
        }
        // 其他状态按原有逻辑
        proceedButton.visibility = View.VISIBLE
        cancelButton.visibility = View.GONE
        when (order.statusValue) {
            "pending_orders" -> {
                proceedButton.text = "接单"
                proceedButton.setDebounceClickListener(1500) { receiveOrder() }
            }
            "engineer_receive" -> {
                proceedButton.text = "开始出发"
                proceedButton.setDebounceClickListener(1500) { startDeparture() }
            }
            "engineer_departure" -> {
                proceedButton.text = "到达现场"
                proceedButton.setDebounceClickListener(1500) { arriveAtSite() }
            }
            else -> {
                proceedButton.text = "填写报告"
                proceedButton.setDebounceClickListener(1500) { handleProceedAction() }
            }
        }
    }
    
    // 设置基本信息
    private fun setupBasicInfo(view: View) {
        // 设置工单状态
        val statusView = view.findViewById<TextView>(R.id.tv_detail_status)
        val progressIndicator = view.findViewById<LinearProgressIndicator>(R.id.progress_status)
        
        // 获取内容区域和底部按钮区域
        val contentLayout = view.findViewById<NestedScrollView>(R.id.nested_scroll_view)?.getChildAt(0) as? LinearLayout
        val buttonLayout = view.findViewById<LinearLayout>(R.id.button_layout)
        
        // 添加日志记录设备组信息
        Log.d("WorkOrderDetail", "设置设备信息 - deviceGroup: '${order.deviceGroup}', machineModel: '${order.machineModel}', serviceType: '${order.serviceType}'")
        
        // 添加状态值日志，帮助调试
        Log.d("WorkOrderDetail", "工单状态 - status: '${order.status}', statusValue: '${order.statusValue}'")
        
        // 根据状态设置UI - 使用statusValue进行判断，并匹配工单列表的颜色
        statusView.text = order.status
        when (order.statusValue) {
            "pending_orders" -> {
                statusView.setBackgroundResource(R.drawable.bg_status_pending)
                progressIndicator.progress = 0
            }
            "engineer_receive" -> {
                // 工程师接单/已分配 - 对应列表中的assigned颜色
                statusView.setBackgroundResource(R.drawable.bg_status_pending)
                progressIndicator.progress = 20
            }
            "engineer_departure" -> {
                // 工程师出发/路途中 - 对应列表中的on_way颜色
                statusView.setBackgroundResource(R.drawable.bg_status_pending)
                progressIndicator.progress = 40
            }
            "engineer_arrive" -> {
                // 工程师到达/维修中 - 对应列表中的in_progress颜色
                //  statusView.setBackgroundResource(R.drawable.bg_status_in_progress)
                statusView.setBackgroundResource(R.drawable.bg_status_pending)
                progressIndicator.progress = 60
            }
            "wait_confirmed_report", "to_be_settled", "wait_audit" -> {
                // 待确认/结算/审核 - 使用橙色（与工单列表一致）
                statusView.setBackgroundResource(R.drawable.bg_status_pending)
                // 根据具体状态细化进度条
                progressIndicator.progress = when(order.statusValue) {
                    "wait_confirmed_report" -> 80
                    else -> 90 // 待结算、待审核都为90%
                }
            }
            "completed" -> {
                // 已完成 - 对应列表中的completed颜色
                statusView.setBackgroundResource(R.drawable.bg_status_completed)
                progressIndicator.progress = 100
            }
            "close" -> {
                // 关闭 - 对应列表中的cancelled颜色
                statusView.setBackgroundResource(R.drawable.bg_status_cancelled)
                progressIndicator.progress = 0
            }
        }
        
        // 设置基本信息
        view.findViewById<TextView>(R.id.tv_order_id).text = order.code
        view.findViewById<TextView>(R.id.tv_detail_customer_name).text = order.customerName
        
        // 记录地址信息
        Log.d("WorkOrderDetail", "显示地址信息 - 原始值: '${order.address}', 显示值: '${order.address ?: "未设置地址"}'")
        view.findViewById<TextView>(R.id.tv_customer_address).text = order.address ?: "未设置地址"

        // 新增：设置客户信息卡片点击事件
        setupCustomerInfoClickListener(view)
    }
    
    // 新增：设置工程师信息板块
    private fun setupEngineerInfo(view: View) {
        // 获取工程师信息相关视图
        val engineerInfoLayout = view.findViewById<LinearLayout>(R.id.layout_engineer_info)
        val noEngineerLayout = view.findViewById<LinearLayout>(R.id.layout_no_engineer)
        val engineerActionsLayout = view.findViewById<LinearLayout>(R.id.layout_engineer_actions)
        val btnAssignEngineer = view.findViewById<Button>(R.id.btn_assign_engineer)
        val btnReassignEngineer = view.findViewById<Button>(R.id.btn_reassign_engineer)
        val engineerNameView = view.findViewById<TextView>(R.id.tv_engineer_name)
        val engineerPhoneLayout = view.findViewById<LinearLayout>(R.id.layout_engineer_phone)
        val engineerPhoneView = view.findViewById<TextView>(R.id.tv_engineer_phone)
        val btnCallEngineer = view.findViewById<ImageButton>(R.id.btn_call_engineer)
        
        // 根据工单是否分配工程师显示不同内容
        if (order.engineerId != null && order.engineerName?.isNotEmpty() == true) {
            // 已分配工程师，显示工程师信息
            engineerInfoLayout.visibility = View.VISIBLE
            noEngineerLayout.visibility = View.GONE
            
            // 设置工程师姓名
            engineerNameView.text = order.engineerName
            
            // 初始隐藏电话布局，待获取到电话后再显示
            engineerPhoneLayout.visibility = View.GONE
            
            // 发起API请求获取工程师详细信息
            workOrderApi.getEngineer(order.engineerId!!).enqueue(object : Callback<ApiResponse<Any>> {
                override fun onResponse(
                    call: Call<ApiResponse<Any>>,
                    response: Response<ApiResponse<Any>>
                ) {
                    if (!isAdded) return // 避免Fragment已分离的问题
                    
                    if (response.isSuccessful && response.body()?.code == 200) {
                        try {
                            // 记录返回数据结构，帮助调试
                            Log.d("WorkOrderDetail", "工程师详情响应: ${response.body()?.data}")
                            
                            val data = response.body()?.data
                            var mobileNumber: String? = null
                            
                            // 尝试从返回数据中获取userManageVo.mobileNumber
                            if (data is Map<*, *>) {
                                val userManageVo = data["userManageVo"] as? Map<*, *>
                                if (userManageVo != null) {
                                    mobileNumber = userManageVo["mobileNumber"] as? String
                                    Log.d("WorkOrderDetail", "从Map中解析到电话: $mobileNumber")
                                }
                            }
                            
                            if (!mobileNumber.isNullOrEmpty()) {
                                // 设置电话信息
                                engineerPhoneLayout.visibility = View.VISIBLE
                                engineerPhoneView.text = mobileNumber
                                
                                // 设置联系按钮点击事件 - 使用防抖
                                btnCallEngineer.setDebounceClickListener {
                try {
                    val intent = Intent(Intent.ACTION_DIAL)
                                        intent.data = Uri.parse("tel:$mobileNumber")
                    startActivity(intent)
                } catch (e: Exception) {
                    Log.e("WorkOrderDetail", "拨打电话失败: "+e.message, e)
                    Toast.makeText(requireContext(), "拨打电话失败: "+e.message, Toast.LENGTH_SHORT).show()
                                    }
            }
                            } else {
                                // 没有电话信息，隐藏电话相关布局
                                engineerPhoneLayout.visibility = View.GONE
                            }
                        } catch (e: Exception) {
                            Log.e("WorkOrderDetail", "解析工程师详情异常: ${e.message}", e)
                            // 解析异常，隐藏电话相关布局
                            engineerPhoneLayout.visibility = View.GONE
                        }
                    } else {
                        Log.e("WorkOrderDetail", "获取工程师详情失败: ${response.code()}")
                        // 请求失败，隐藏电话相关布局
                        engineerPhoneLayout.visibility = View.GONE
                    }
                }
                
                override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                    if (!isAdded) return
                    Log.e("WorkOrderDetail", "获取工程师详情网络请求失败", t)
                    // 请求失败，隐藏电话相关布局
                    engineerPhoneLayout.visibility = View.GONE
                }
            })
            
            // 根据页面类型和工单状态决定是否显示操作按钮
            // 从待接工单进入时不显示转派按钮
            if (pageType == "pending") {
                engineerActionsLayout.visibility = View.GONE
                btnAssignEngineer.visibility = View.GONE
                btnReassignEngineer.visibility = View.GONE
                return
            }
            
            // 只在"我的工单"或"工单管理"页面显示操作按钮
            if (!isFromMyOrders && pageType != "my" && pageType != null) {
                engineerActionsLayout.visibility = View.GONE
                btnAssignEngineer.visibility = View.GONE
                btnReassignEngineer.visibility = View.GONE
                return
            }
            
            // 根据工单状态决定是否显示转派按钮
            if (!order.statusValue.equals("wait_confirmed_report") && 
                !order.statusValue.equals("to_be_settled") && 
                !order.statusValue.equals("completed") && 
                !order.statusValue.equals("close")) {
                // 显示转派按钮
                engineerActionsLayout.visibility = View.VISIBLE
                btnReassignEngineer.visibility = View.VISIBLE
                btnAssignEngineer.visibility = View.GONE
                
                // 设置转派按钮点击事件 - 使用防抖
                btnReassignEngineer.setDebounceClickListener {
                    showReassignEngineerDialog(order.id)
                }
            } else {
                // 隐藏操作按钮区域
                engineerActionsLayout.visibility = View.GONE
            }
        } else {
            // 未分配工程师，显示提示和分配按钮
            engineerInfoLayout.visibility = View.GONE
            noEngineerLayout.visibility = View.VISIBLE
            
            // 从待接工单进入时不显示分配按钮
            if (pageType == "pending") {
                engineerActionsLayout.visibility = View.GONE
                btnAssignEngineer.visibility = View.GONE
                btnReassignEngineer.visibility = View.GONE
                return
            }
            
            // 仅对未分配且状态为待接单的工单显示分配按钮
            if (order.statusValue.equals("pending_orders") || 
                (order.engineerId == null && order.status.contains("待") && !order.status.contains("确认"))) {
                engineerActionsLayout.visibility = View.VISIBLE
                btnAssignEngineer.visibility = View.VISIBLE
                btnReassignEngineer.visibility = View.GONE
                
                // 设置分配按钮点击事件 - 使用防抖
                btnAssignEngineer.setDebounceClickListener {
                    showAssignEngineerDialog(order.id)
                }
            } else {
                // 隐藏操作按钮区域
                engineerActionsLayout.visibility = View.GONE
            }
        }
        
        // 记录工程师信息日志
        Log.d("WorkOrderDetail", "工程师信息 - 工程师ID: '${order.engineerId}', 工程师姓名: '${order.engineerName}'")
    }

    // 获取工程师电话方法
    private fun getEngineerPhone(): String {
        // 由于RepairOrder类中可能没有engineer属性，我们尝试从其他可能的属性中获取电话
        
        // 1. 检查原始数据中是否有工程师电话相关字段
        try {
            // 可能的电话属性名
            val possiblePhoneProps = listOf(
                "engineerPhone", "engineerMobile", "engineerTel",
                "engineerContactPhone", "engineerContact"
            )
            
            // 通过反射尝试获取可能存在的电话属性
            for (propName in possiblePhoneProps) {
                try {
                    val method = order.javaClass.getMethod("get${propName.replaceFirstChar {
                        if (it.isLowerCase()) it.titlecase(
                            Locale.getDefault()
                        ) else it.toString()
                    }}")
                    val value = method.invoke(order)
                    if (value != null && value.toString().isNotEmpty()) {
                        return value.toString()
                    }
                } catch (e: Exception) {
                    // 忽略反射异常，继续尝试下一个属性
                }
            }
            
            // 2. 如果order是从Map解析的，尝试检查原始Map中是否有这些字段
            val engineerId = order.engineerId
            if (engineerId != null) {
                // 如果有工程师ID，可能还有其他工程师信息
                // 尝试在日志中记录所有可用的字段名，方便调试
                Log.d("WorkOrderDetail", "工程师ID存在，尝试获取电话信息")
            }
        } catch (e: Exception) {
            Log.e("WorkOrderDetail", "获取工程师电话时发生错误", e)
        }
        
        // 如果没有找到，返回空字符串
        return ""
    }
    
    // 设置设备信息
    private fun setupDeviceInfo(view: View) {
        // 设置设备信息
        view.findViewById<TextView>(R.id.tv_detail_device_group).text = order.deviceGroup
        view.findViewById<TextView>(R.id.tv_detail_machine_model).text = order.machineModel
        view.findViewById<TextView>(R.id.tv_detail_service_type).text = getServiceTypeText(order.serviceType)
        
        // 获取设备图片ImageView
        val deviceImageView = view.findViewById<ImageView>(R.id.img_device)
        
        // 检查是否有设备照片URL，如果没有则隐藏设备图片
        if(order.deviceGroupImg.isNullOrEmpty()) {
            deviceImageView.visibility = View.GONE
        } else {
            deviceImageView.visibility = View.VISIBLE
            // 加载设备图片
            Glide.with(requireContext())
                .load(order.deviceGroupImg)
                .into(deviceImageView)
        }

        // 新增：设置设备信息卡片点击事件
        setupDeviceInfoClickListener(view)

        // 设置计数器显示
        setupCounters(view, order)
        
        // 添加数据可视化内容 - 放在工单状态卡片的下方位置
        setupVisualization(view)
        
        // 设置故障信息
        view.findViewById<TextView>(R.id.tv_fault_desc).text = order.problemDesc.ifEmpty { "未提供故障描述" }
        
        // 设置故障照片
        val faultImagesRecyclerView = view.findViewById<RecyclerView>(R.id.recycler_fault_images)
        val faultImagesLayout = view.findViewById<LinearLayout>(R.id.layout_fault_images)
        
        if (order.faultPhotos.isNotEmpty()) {
            Log.d("RepairOrderDetail", "显示故障照片：${order.faultPhotos.size}张")
            faultImagesLayout.visibility = View.VISIBLE
            faultImagesRecyclerView.visibility = View.VISIBLE
            
            // 设置RecyclerView布局管理器 - 使用水平GridLayoutManager，每行显示1张照片（实际是水平方向）
            val layoutManager = GridLayoutManager(requireContext(), 1, GridLayoutManager.HORIZONTAL, false)
            faultImagesRecyclerView.layoutManager = layoutManager

            // 添加Item间距装饰器，防止重叠
            if (faultImagesRecyclerView.itemDecorationCount == 0) {
                faultImagesRecyclerView.addItemDecoration(object : RecyclerView.ItemDecoration() {
                    private val spacing = (4 * resources.displayMetrics.density).toInt() // 4dp in pixels

                    override fun getItemOffsets(
                        outRect: Rect,
                        view: View,
                        parent: RecyclerView,
                        state: RecyclerView.State
                    ) {
                        outRect.right = spacing
                        if (parent.getChildAdapterPosition(view) == 0) {
                            outRect.left = 0
                        }
                    }
                })
            }

            // 设置适配器
            val adapter = FaultImageAdapter(order.faultPhotos) { photoItem ->
                // 点击照片处理 - 使用现有的ImageViewerDialog显示大图并支持滑动查看其他图片
                val currentIndex = order.faultPhotos.indexOf(photoItem)
                val imageUrls = order.faultPhotos.map { it.url }
                val dialog = com.example.repairorderapp.ui.common.ImageViewerDialog(
                    requireContext(),
                    imageUrls,
                    currentIndex
                )
                dialog.show()
            }
            faultImagesRecyclerView.adapter = adapter
        } else {
            Log.d("RepairOrderDetail", "没有故障照片可显示")
            faultImagesLayout.visibility = View.GONE
            faultImagesRecyclerView.visibility = View.GONE
        }
        
        // 设置时间信息
        view.findViewById<TextView>(R.id.tv_detail_create_time).text = formatDateTimeIso(order.createTime)
        view.findViewById<TextView>(R.id.tv_expected_time).text = if (order.expectArriveTime.isNotEmpty()) {
            TimeUtil.formatTime(parseDateTime(order.expectArriveTime))
        } else {
            "未指定"
        }
        view.findViewById<TextView>(R.id.tv_scheduled_time).text = if (order.prospectArriveTime.isNotEmpty()) {
            TimeUtil.formatTime(parseDateTime(order.prospectArriveTime))
        } else {
            order.startTime?.let { formatDateTimeIso(it) } ?: "未安排"
        }
        // 设置费用信息
        setupFeeDisplay(view, order)  
        
        // 历史维修记录按钮 - 使用防抖
        val btnViewHistory = view.findViewById<TextView>(R.id.btn_view_history)
        btnViewHistory.setDebounceClickListener {
            navigateToDeviceMaintenanceList()
        }
        
        // 设置零件更换记录按钮 - 使用防抖
        val btnViewReplaceRecord = view.findViewById<TextView?>(R.id.btn_view_replace_record)
        btnViewReplaceRecord?.setDebounceClickListener {
            showReplaceRecordDialog()
        }
    }
    
    // 设置维修报告按钮
    private fun setupReportButton(view: View) {
        val btnViewReport = view.findViewById<Button>(R.id.btn_view_report)
        
        // 对于有维修报告的工单（已完成、待确认报告等状态），显示维修报告按钮
        if (order.statusValue == "wait_confirmed_report" || 
            order.statusValue == "to_be_settled" || 
            order.statusValue == "completed" || 
            order.status.contains("完成") || 
            order.status.contains("确认")) {
            
            btnViewReport.visibility = View.VISIBLE
            btnViewReport.setDebounceClickListener {
                // 跳转到维修报告详情页面
                com.example.repairorderapp.ui.report.RepairReportDetailActivity.start(requireContext(), order.id)
            }
        } else {
            btnViewReport.visibility = View.GONE
        }
    }
    
    // 设置导航按钮
    private fun setupNavigationButton(view: View) {
        // 添加触感反馈 - 使用防抖
        view.findViewById<ImageView>(R.id.btn_navigation).setDebounceClickListener {
            it.performHapticFeedback(HapticFeedbackConstants.VIRTUAL_KEY)
            
            // 优化：使用Handler将耗时操作从UI点击事件中分离出来
            Handler(Looper.getMainLooper()).post {
                // 检查是否有经纬度信息 - 修改为允许0值的情况
                if (order.latitude == 0.0 && order.longitude == 0.0) {
                    Log.d("WorkOrderDetail", "缺少有效的地址经纬度: lat=${order.latitude}, lng=${order.longitude}")
                    Toast.makeText(requireContext(), "未设置客户位置信息", Toast.LENGTH_SHORT).show()
                } else {
                    // 打开地图导航
                    openNavigation()
                }
            }
        }
    }
    
    // 设置联系按钮
    private fun setupContactButton(view: View) {
        // 显示报修人姓名，如果为空则显示报修电话
        view.findViewById<TextView>(R.id.tv_reporter)?.text = order.reporter.ifEmpty { 
            if (order.reporterMobile.isNotEmpty()) order.reporterMobile else "未设置" 
        }
        
        // 添加日志记录报修人信息
        Log.d("WorkOrderDetail", "显示报修人信息 - 报修人: '${order.reporter}', 电话: '${order.reporterMobile}'")
        
        // 添加联系用户点击事件 - 使用防抖
        view.findViewById<ImageButton>(R.id.btn_call_reporter)?.setDebounceClickListener {
            // 添加触感反馈
            it.performHapticFeedback(HapticFeedbackConstants.VIRTUAL_KEY)
            
            if (order.reporterMobile.isNotEmpty()) {
                try {
                    val intent = Intent(Intent.ACTION_DIAL)
                    intent.data = Uri.parse("tel:${order.reporterMobile}")
                    startActivity(intent)
                } catch (e: Exception) {
                    Log.e("WorkOrderDetail", "拨打电话失败: ${e.message}", e)
                    Toast.makeText(requireContext(), "拨打电话失败: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            } else {
                Toast.makeText(requireContext(), "报修人电话号码未设置", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun getServiceTypeText(serviceType: String): String {
        return when (serviceType) {
            "regular" -> "常规保养"
            "warranty" -> "质保维修"
            "paid" -> "付费维修"
            "installation" -> "安装调试"
            "troubleshooting" -> "故障检修"
            "consulting" -> "咨询培训"
            else -> serviceType
        }
    }
    
    private fun setupCounters(view: View, order: RepairOrder) {
        // 黑白计数器（所有类型都显示）
        val bwCounterLayout = view.findViewById<View>(R.id.layout_bw_counter)
        val bwCounterText = view.findViewById<TextView>(R.id.tv_bw_counter)
        bwCounterText.text = formatNumber(order.bwCounter)
        bwCounterLayout.visibility = View.VISIBLE
        
        // 彩色计数器（彩色和五色类型显示）
        val colorCounterLayout = view.findViewById<View>(R.id.layout_color_counter)
        val colorCounterText = view.findViewById<TextView>(R.id.tv_color_counter)
        colorCounterText.text = formatNumber(order.colorCounter)
        
        // 第五色计数器（仅五色类型显示）
        val fifthColorCounterLayout = view.findViewById<View>(R.id.layout_fifth_color_counter)
        val fifthColorCounterText = view.findViewById<TextView>(R.id.tv_fifth_color_counter)
        fifthColorCounterText.text = formatNumber(order.fifthColorCounter)
        
        // 根据色彩类型决定显示哪些计数器
        when {
            order.colorType.contains("黑白") -> {
                // 黑白类型只显示黑白计数器
                colorCounterLayout.visibility = View.GONE
                fifthColorCounterLayout.visibility = View.GONE
            }
            order.colorType.contains("彩色") && !order.colorType.contains("五色") -> {
                // 彩色类型显示黑白和彩色计数器
                colorCounterLayout.visibility = View.VISIBLE
                fifthColorCounterLayout.visibility = View.GONE
            }
            order.colorType.contains("五色") -> {
                // 五色类型显示全部计数器
                colorCounterLayout.visibility = View.VISIBLE
                fifthColorCounterLayout.visibility = View.VISIBLE
            }
            else -> {
                // 默认只显示黑白计数器
                colorCounterLayout.visibility = View.GONE
                fifthColorCounterLayout.visibility = View.GONE
            }
        }
    }
    
    private fun formatNumber(number: Int): String {
        // 实现格式化数字的逻辑
        return number.toString()
    }
    
    private fun showCancelConfirmDialog() {
        // 实现显示取消工单确认对话框的逻辑
    }
    
    private fun handleProceedAction() {
        // 根据工单状态处理不同的操作 - 使用statusValue进行判断
        when (order.statusValue) {
            "pending_orders" -> {
                // 接单逻辑
                receiveOrder()
            }
            "engineer_receive" -> {
                // 开始出发逻辑
                startDeparture()
            }
            "engineer_departure" -> {
                // 到达现场逻辑
                arriveAtSite()
            }
            "engineer_arrive" -> {
                // 完成维修逻辑
                completeRepair()
            }
            "wait_confirmed_report" -> {
                // 客户已确认逻辑
                confirmByCustomer()
            }
            else -> {
                // 如果使用statusValue无法判断，回退到使用status文本判断
                when {
                    order.status.contains("待") && !order.status.contains("确认") -> {
                        // 接单逻辑
                        receiveOrder()
                    }
                    order.status.contains("分配") -> {
                        // 开始出发逻辑
                        startDeparture()
                    }
                    order.status.contains("路途") -> {
                        // 到达现场逻辑
                        arriveAtSite()
                    }
                    order.status.contains("维修中") -> {
                        // 完成维修逻辑
                        completeRepair()
                    }
                    order.status.contains("等待") && order.status.contains("确认") -> {
                        // 客户已确认逻辑
                        confirmByCustomer()
                    }
                    else -> {
                        // 未知状态，显示提示
                        Toast.makeText(requireContext(), "暂不支持此操作", Toast.LENGTH_SHORT).show()
                    }
                }
            }
        }
    }
    
    // 实现各个状态的处理方法
    private fun receiveOrder() {
        // 调用工程师接单API
        val params = HashMap<String, String>()
        params["id"] = order.id
        workOrderApi.acceptWorkOrder(params).enqueue(object : Callback<ApiResponse<Any>> {
            override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                if (response.isSuccessful && response.body()?.code == 200) {
                    Toast.makeText(requireContext(), "接单成功", Toast.LENGTH_SHORT).show()
                    loadWorkOrderDetail()
                } else {
                    Toast.makeText(requireContext(), "接单失败: ${response.body()?.msg ?: "未知错误"}", Toast.LENGTH_SHORT).show()
                }
            }
            override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    private fun startDeparture() {
        // 调用工程师出发API
        val params = HashMap<String, String>()
        params["id"] = order.id
        workOrderApi.departureWorkOrder(params).enqueue(object : Callback<ApiResponse<Any>> {
            override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                if (response.isSuccessful && response.body()?.code == 200) {
                    Toast.makeText(requireContext(), "已出发", Toast.LENGTH_SHORT).show()
                    loadWorkOrderDetail()
                } else {
                    Toast.makeText(requireContext(), "出发失败: ${response.body()?.msg ?: "未知错误"}", Toast.LENGTH_SHORT).show()
                }
            }
            override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    private fun arriveAtSite() {
        // 调用工程师到达API
        val params = HashMap<String, String>()
        params["id"] = order.id
        workOrderApi.arriveWorkOrder(params).enqueue(object : Callback<ApiResponse<Any>> {
            override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                if (response.isSuccessful && response.body()?.code == 200) {
                    Toast.makeText(requireContext(), "已到达现场", Toast.LENGTH_SHORT).show()
                    loadWorkOrderDetail()
                } else {
                    Toast.makeText(requireContext(), "到达失败: ${response.body()?.msg ?: "未知错误"}", Toast.LENGTH_SHORT).show()
                }
            }
            override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    
    private fun completeRepair() {
        Toast.makeText(requireContext(), "完成维修功能开发中...", Toast.LENGTH_SHORT).show()
        // TODO: 实现完成维修API调用
    }
    
    private fun confirmByCustomer() {
        Toast.makeText(requireContext(), "客户确认功能开发中...", Toast.LENGTH_SHORT).show()
        // TODO: 实现客户确认API调用
    }
    
    private fun openNavigation() {
        try {
            if (order.latitude == 0.0 && order.longitude == 0.0 && order.address.isNullOrEmpty()) {
                Toast.makeText(requireContext(), "没有可用的地址信息", Toast.LENGTH_SHORT).show()
                return
            }
            
            val hasValidCoordinates = order.latitude != 0.0 && order.longitude != 0.0
            // 添加客户名称到地址信息前面
            val destinationAddress = if (order.customerName.isNotEmpty()) {
                "${order.customerName} ${order.address ?: ""}"
            } else {
                order.address ?: ""
            }
            val latitude = order.latitude
            val longitude = order.longitude
            
            Log.d("WorkOrderDetail", "打开导航 - 经度: $longitude, 纬度: $latitude, 地址: $destinationAddress")
            
            // 保存所有可能的导航选项
            val navigationOptions = mutableListOf<NavigationOption>()
            
            // 检查是否安装了高德地图，并添加导航选项
            if (isAppInstalled("com.autonavi.minimap")) {
                val amapUri = if (hasValidCoordinates) {
                    // 使用路线规划而非直接导航
                    Uri.parse("androidamap://route?sourceApplication=RepairOrderApp&dlat=$latitude&dlon=$longitude&dname=$destinationAddress&dev=0&t=0")
                } else {
                    Uri.parse("androidamap://poi?sourceApplication=RepairOrderApp&keywords=$destinationAddress&dev=0")
                }
                
                navigationOptions.add(
                    NavigationOption(
                        name = "高德地图",
                        packageName = "com.autonavi.minimap",
                        uri = amapUri
                    )
                )
            }
            
            // 检查是否安装了百度地图，并添加导航选项
            if (isAppInstalled("com.baidu.BaiduMap")) {
                val baiduUri = if (hasValidCoordinates) {
                    // 使用路线规划
                    Uri.parse("baidumap://map/direction?origin=我的位置&destination=name:$destinationAddress|latlng:$latitude,$longitude&mode=driving&coord_type=bd09ll&src=andr.RepairOrderApp")
                } else {
                    Uri.parse("baidumap://map/geocoder?address=$destinationAddress&src=andr.RepairOrderApp")
                }
                
                navigationOptions.add(
                    NavigationOption(
                        name = "百度地图",
                        packageName = "com.baidu.BaiduMap",
                        uri = baiduUri
                    )
                )
            }
            
            // 检查是否安装了腾讯地图，并添加导航选项
            if (isAppInstalled("com.tencent.map")) {
                val tencentUri = if (hasValidCoordinates) {
                    // 使用路线规划
                    Uri.parse("qqmap://map/routeplan?type=drive&from=我的位置&to=$destinationAddress&tocoord=$latitude,$longitude&referer=RepairOrderApp")
                } else {
                    Uri.parse("qqmap://map/search?keyword=$destinationAddress&referer=RepairOrderApp")
                }
                
                navigationOptions.add(
                    NavigationOption(
                        name = "腾讯地图",
                        packageName = "com.tencent.map",
                        uri = tencentUri
                    )
                )
            }
            
            // 检查是否安装了Google地图，并添加导航选项
            if (isAppInstalled("com.google.android.apps.maps")) {
                val googleUri = if (hasValidCoordinates) {
                    // 使用路线规划
                    Uri.parse("https://www.google.com/maps/dir/?api=1&destination=$latitude,$longitude&travelmode=driving&destination_name=$destinationAddress")
                } else {
                    Uri.parse("https://www.google.com/maps/dir/?api=1&destination=$destinationAddress&travelmode=driving")
                }
                
                navigationOptions.add(
                    NavigationOption(
                        name = "Google地图",
                        packageName = "com.google.android.apps.maps",
                        uri = googleUri
                    )
                )
            }
            
            // 添加系统默认地图选项
            val geoUri = if (hasValidCoordinates) {
                Uri.parse("geo:$latitude,$longitude?q=$latitude,$longitude($destinationAddress)")
            } else {
                Uri.parse("geo:0,0?q=$destinationAddress")
            }
            
            navigationOptions.add(
                NavigationOption(
                    name = "系统默认地图",
                    packageName = "",
                    uri = geoUri
                )
            )
            
            // 如果只有一个选项，直接启动
            if (navigationOptions.size == 1) {
                startNavigation(navigationOptions[0])
            } else if (navigationOptions.isNotEmpty()) {
                // 显示选择对话框
                showNavigationOptionsDialog(navigationOptions)
            } else {
                // 没有可用的地图应用
                Toast.makeText(requireContext(), "没有安装可用的地图应用", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e("WorkOrderDetail", "打开导航时出错: ${e.message}", e)
            Toast.makeText(requireContext(), "打开地图导航失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    // 导航选项数据类 - 已被NavigationApp取代，保留这里是为了向后兼容
    data class NavigationOption(
        val name: String,
        val packageName: String,
        val uri: Uri
    )
    
    // 显示导航选项对话框
    private fun showNavigationOptionsDialog(options: List<NavigationOption>) {
        // 获取系统已安装的导航应用
        val navigationApps = getInstalledNavigationApps(options)
        
        // 创建自定义对话框
        val dialog = Dialog(requireContext())
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setContentView(R.layout.dialog_navigation_app_selection)
        
        // 设置对话框宽度
        val window = dialog.window
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        
        // 设置RecyclerView
        val recyclerView = dialog.findViewById<RecyclerView>(R.id.recycler_navigation_apps)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        
        // 设置适配器
        val adapter = NavigationAppAdapter(navigationApps) { app ->
            dialog.dismiss()
            startNavigation(NavigationOption(app.appName, app.packageName, app.uri))
        }
        recyclerView.adapter = adapter
        
        // 设置取消按钮 - 使用防抖
        dialog.findViewById<Button>(R.id.btn_cancel).setDebounceClickListener {
            dialog.dismiss()
        }
        
        // 显示对话框
        dialog.show()
    }
    
    // 获取手机中已安装的导航应用
    private fun getInstalledNavigationApps(options: List<NavigationOption>): List<NavigationApp> {
        val packageManager = requireContext().packageManager
        val result = mutableListOf<NavigationApp>()
        
        // 常见导航应用包名列表
        val knownNavigationApps = mapOf(
            "com.autonavi.minimap" to "高德地图",
            "com.baidu.BaiduMap" to "百度地图",
            "com.tencent.map" to "腾讯地图",
            "com.google.android.apps.maps" to "Google地图",
            "com.waze" to "Waze",
            "com.mapswithme.maps.pro" to "MAPS.ME",
            "com.here.app.maps" to "HERE WeGo"
        )
        
        // 检查已知导航应用是否已安装
        for ((packageName, appName) in knownNavigationApps) {
            try {
                val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
                val appIcon = packageManager.getApplicationIcon(applicationInfo)
                val appLabel = packageManager.getApplicationLabel(applicationInfo).toString()
                
                // 查找对应的URI
                val option = options.find { it.packageName == packageName }
                if (option != null) {
                    result.add(NavigationApp(appLabel, packageName, appIcon, option.uri))
                }
            } catch (e: Exception) {
                // 应用未安装，忽略
                Log.d("NavigationApps", "应用未安装: $packageName")
            }
        }
        
        // 添加系统默认地图
        val defaultMapOption = options.find { it.packageName.isEmpty() }
        if (defaultMapOption != null) {
            result.add(NavigationApp(
                "系统默认地图",
                "",
                requireContext().getDrawable(android.R.drawable.ic_dialog_map),
                defaultMapOption.uri
            ))
        }
        
        // 如果没有找到任何已安装的导航应用，则使用原始选项列表
        if (result.isEmpty()) {
            for (option in options) {
                var icon: Drawable? = null
                var appName = option.name
                
                // 尝试获取应用图标和名称（如果包名不为空）
                if (option.packageName.isNotEmpty()) {
                    try {
                        val applicationInfo = packageManager.getApplicationInfo(option.packageName, 0)
                        icon = packageManager.getApplicationIcon(applicationInfo)
                        appName = packageManager.getApplicationLabel(applicationInfo).toString()
                    } catch (e: Exception) {
                        icon = requireContext().getDrawable(android.R.drawable.ic_dialog_map)
                    }
                } else {
                    icon = requireContext().getDrawable(android.R.drawable.ic_dialog_map)
                }
                
                result.add(NavigationApp(appName, option.packageName, icon, option.uri))
            }
        }
        
        return result
    }
    
    // 启动导航
    private fun startNavigation(option: NavigationOption) {
        try {
            val intent = Intent(Intent.ACTION_VIEW, option.uri)
            if (option.packageName.isNotEmpty()) {
                intent.setPackage(option.packageName)
            }
            startActivity(intent)
        } catch (e: Exception) {
            Log.e("WorkOrderDetail", "启动${option.name}导航失败: ${e.message}", e)
            Toast.makeText(requireContext(), "启动导航失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    // 检查应用是否已安装
    private fun isAppInstalled(packageName: String): Boolean {
        return try {
            requireActivity().packageManager.getApplicationInfo(packageName, 0)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    // 处理会话过期的方法
    private fun handleSessionExpired() {
        Log.e("WorkOrderDetail", "会话已过期，将由TokenInterceptor处理刷新令牌")
        
        // 安全检查：确保Fragment仍然附加到上下文
        if (!isAdded) {
            Log.d("WorkOrderDetail", "Fragment已分离，不处理会话过期")
            return
        }
        
        try {
            Toast.makeText(requireContext(), "会话已过期，正在尝试刷新...", Toast.LENGTH_SHORT).show()
            
            // 在组件中不需要处理令牌刷新，TokenInterceptor会自动处理
            // 只需重新加载数据
            Handler(Looper.getMainLooper()).postDelayed({
                // 再次检查Fragment是否仍然附加到上下文
                if (isAdded) {
                    // 延迟一秒后刷新数据，给TokenInterceptor时间刷新令牌
                    loadWorkOrderDetail()
                }
            }, 1000)
        } catch (e: Exception) {
            Log.e("WorkOrderDetail", "处理会话过期时发生错误: ${e.message}", e)
        }
    }
    
    // 新增设置费用显示的方法
    private fun setupFeeDisplay(view: View, order: RepairOrder) {
        // 上门费
        view.findViewById<TextView>(R.id.tv_visit_fee)?.apply {
            text = formatFee(order.visitPay)
            visibility = if (order.visitPay > 0) View.VISIBLE else View.GONE
        }
        
        // 远程上门费
        view.findViewById<TextView>(R.id.tv_remote_fee)?.apply {
            text = formatFee(order.longWayVisitPay)
            visibility = if (order.longWayVisitPay > 0) View.VISIBLE else View.GONE
        }
        
        // 维修诊断费
        view.findViewById<TextView>(R.id.tv_diagnosis_fee)?.apply {
            text = formatFee(order.repairPay)
            visibility = if (order.repairPay > 0) View.VISIBLE else View.GONE
        }
        
        // 维修耗材费
        view.findViewById<TextView>(R.id.tv_materials_fee)?.apply {
            text = formatFee(order.itemPay)
            visibility = if (order.itemPay > 0) View.VISIBLE else View.GONE
        }
        
        // 工程师追加费用
        view.findViewById<TextView>(R.id.tv_additional_fee)?.apply {
            text = formatFee(order.engineerAdditionalPay)
            visibility = if (order.engineerAdditionalPay > 0) View.VISIBLE else View.GONE
        }
        
        // 工程师减免费用
        view.findViewById<TextView>(R.id.tv_discount_fee)?.apply {
            text = formatFee(-order.derateAmount) // 使用负数表示减免
            visibility = if (order.derateAmount > 0) View.VISIBLE else View.GONE
        }
        
        // 会员减免
        view.findViewById<TextView>(R.id.tv_member_discount)?.apply {
            text = formatFee(-order.discountAmount) // 负数以表示减免
            visibility = if (order.discountAmount > 0) View.VISIBLE else View.GONE
        }
        
        // 客户追加报酬
        view.findViewById<TextView>(R.id.tv_customer_extra_fee)?.apply {
            text = formatFee(order.additionalPay)
            visibility = if (order.additionalPay > 0) View.VISIBLE else View.GONE
        }
        
        // 应付维修费用 - 修改为直接使用API返回的totalPay字段
        view.findViewById<TextView>(R.id.tv_total_payable)?.apply {
            text = formatFee(order.totalPay)
        }
        
        // 实付维修费用 - 修改为直接使用API返回的actualPay字段
        view.findViewById<TextView>(R.id.tv_total_paid)?.apply {
            text = formatFee(order.actualPay)
        }
    }
    
    // 格式化金额方法
    private fun formatFee(amount: Double): String {
        // 处理数值非常接近0的情况（浮点数精度问题）
        val roundedAmount = if (Math.abs(amount) < 0.001) 0.0 else amount
        
        return if (roundedAmount < 0) {
            // 负数情况（如减免费用），将负号放在¥符号前面
            String.format("-¥%.2f", Math.abs(roundedAmount))
        } else if (roundedAmount == 0.0 && amount < 0) {
            // 特殊处理：当金额实际上是负数但四舍五入后为0时
            // 仍然显示负号在¥前面，确保视觉一致性
            "-¥0.00"
        } else {
            // 正数情况
            String.format("¥%.2f", roundedAmount)
        }
    }
    
    // 添加照片适配器
    private inner class FaultImageAdapter(
        private val photos: List<PhotoItem>,
        private val onClick: (PhotoItem) -> Unit
    ) : RecyclerView.Adapter<FaultImageAdapter.ViewHolder>() {
        
        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val imageView: ImageView = itemView.findViewById(R.id.iv_fault_image)
            val progressBar: ProgressBar = itemView.findViewById(R.id.progress_image_loading)
        }
        
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_fault_image, parent, false)
            return ViewHolder(view)
        }
        
        override fun getItemCount(): Int = photos.size
        
        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val photoItem = photos[position]
            
            // 开始加载前显示进度条
            holder.progressBar.visibility = View.VISIBLE
            
            // 使用Glide加载图片
            Glide.with(holder.itemView.context)
                .load(photoItem.url)
                .listener(object : RequestListener<Drawable> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Drawable>?,
                        isFirstResource: Boolean
                    ): Boolean {
                        holder.progressBar.visibility = View.GONE
                        Log.e("FaultImageAdapter", "图片加载失败: ${photoItem.url}", e)
                        return false
                    }
                    
                    override fun onResourceReady(
                        resource: Drawable?,
                        model: Any?,
                        target: Target<Drawable>?,
                        dataSource: DataSource?,
                        isFirstResource: Boolean
                    ): Boolean {
                        holder.progressBar.visibility = View.GONE
                        return false
                    }
                })
                .placeholder(R.drawable.placeholder_image) // 添加占位图
                .error(R.drawable.error_image)            // 添加错误图
                .centerCrop()
                .into(holder.imageView)
            
            // 设置点击事件 - 使用防抖
            holder.itemView.setDebounceClickListener {
                onClick(photoItem)
            }
        }
    }
    
//    // 添加全屏显示图片的方法
//    private fun showFullScreenImage(imageUrl: String) {
//        try {
//            // 创建一个Dialog或使用自定义的PhotoViewerActivity来显示大图
//            val dialog = Dialog(requireContext(), R.style.FullScreenDialogStyle)
//            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
//            dialog.setContentView(R.layout.dialog_full_screen_image)
//            dialog.window?.setLayout(
//                ViewGroup.LayoutParams.MATCH_PARENT,
//                ViewGroup.LayoutParams.MATCH_PARENT
//            )
//
//            val photoView = dialog.findViewById<com.github.chrisbanes.photoview.PhotoView>(R.id.iv_full_screen)
//            val progressBar = dialog.findViewById<ProgressBar>(R.id.progress_full_screen)
//            val closeButton = dialog.findViewById<ImageButton>(R.id.btn_close)
//
//            // 关闭按钮事件 - 使用防抖
//            closeButton.setDebounceClickListener {
//                dialog.dismiss()
//            }
//
//            // 配置PhotoView
//            photoView.maximumScale = 5.0f  // 降低最大缩放比例，避免过度缩放导致的问题
//            photoView.mediumScale = 2.0f   // 降低中间缩放比例
//            photoView.minimumScale = 1.0f  // 保持最小缩放比例
//
//            // 启用单击关闭对话框功能
//            photoView.setOnPhotoTapListener { _, _, _ ->
//                dialog.dismiss()
//            }
//
//            // 关闭矩阵重置，避免快速移动
//            photoView.setAllowParentInterceptOnEdge(true)
//
//            // 使用Glide加载图片
//            Glide.with(requireContext())
//                .load(imageUrl)
//                .listener(object : RequestListener<Drawable> {
//                    override fun onLoadFailed(
//                        e: GlideException?,
//                        model: Any?,
//                        target: Target<Drawable>?,
//                        isFirstResource: Boolean
//                    ): Boolean {
//                        progressBar.visibility = View.GONE
//                        Toast.makeText(requireContext(), "图片加载失败", Toast.LENGTH_SHORT).show()
//                        return false
//                    }
//
//                    override fun onResourceReady(
//                        resource: Drawable?,
//                        model: Any?,
//                        target: Target<Drawable>?,
//                        dataSource: DataSource?,
//                        isFirstResource: Boolean
//                    ): Boolean {
//                        progressBar.visibility = View.GONE
//                        // 显示简短提示
//                        Toast.makeText(requireContext(), "点击图片退出", Toast.LENGTH_SHORT).show()
//                        return false
//                    }
//                })
//                .into(photoView)
//
//            dialog.show()
//        } catch (e: Exception) {
//            Log.e("RepairOrderDetail", "显示全屏图片失败: ${e.message}", e)
//            Toast.makeText(requireContext(), "图片预览失败", Toast.LENGTH_SHORT).show()
//        }
//    }
    
    // 添加WorkOrderItem数据类
    private data class WorkOrderItem(
        val id: String,
        val code: String? = null,
        val customerId: String? = null,
        val customerName: String? = null,
        val customer: Customer? = null,
        val engineerId: EngineerInfo? = null,
        val deviceNumber: String? = null,
        val brand: String? = null,
        val machine: String? = null,
        val deviceGroup: DictItem? = null,
        val deviceGroupId: String? = null, // 新增
        val deviceGroupImg: String? = null,
        val latitude: Double? = null,
        val longitude: Double? = null,
        val serType: DictItem? = null,
        val status: DictItem? = null,
        val excDesc: String? = null,
        val createdAt: String,
        val beginTime: String? = null,
        val endTime: String? = null,
        val productId: String? = null,
        val productInfo: String? = null,
        val excPics: List<Map<String, Any>>? = null,
        // 时间信息
        val expectArriveTime: String? = null,
        val prospectArriveTime: String? = null,
        val arriveTime: Date? = null,
        val waitConfirmTime: Date? = null,
        val finishTime: Date? = null,
        // 费用信息
        val visitPay: Double? = null,
        val longWayVisitPay: Double? = null,
        val repairPay: Double? = null,
        val itemPay: Double? = null,
        val actualReplacePay: Double? = null,
        val engineerAdditionalPay: Double? = null,
        val derateAmount: Double? = null,
        val discountAmount: Double? = null,
        val additionalPay: Double? = null,
        val totalPay: Double? = null,
        val actualPay: Double? = null,
        // 报修人信息
        val reporter: String? = null,
        val reporterMobile: String? = null,
        // 工单状态时间字段
        val orderReceiveTime: String? = null,
        val departureTime: String? = null,
        val actualArriveTime: String? = null,
        val sendReportTime: String? = null,
        val confirmReportTime: String? = null,
        val completedAt: String? = null
    )
    
    private data class Customer(
        val id: String,
        val name: String,
        val address: String
    )
    
    private data class EngineerInfo(
        val id: String,
        val name: String
    )
    
    private data class DictItem(
        val value: String,
        val label: String
    )
    
    /**
     * 调整内容区域底部边距，避免按钮遮挡内容
     */
    private fun adjustContentBottomPadding(contentLayout: LinearLayout?, buttonLayout: LinearLayout?) {
        if (contentLayout != null && buttonLayout != null) {
            // 测量按钮区域高度
            buttonLayout.measure(
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            )
            val buttonHeight = buttonLayout.measuredHeight
            
            // 设置内容区域底部内边距
            contentLayout.setPadding(
                contentLayout.paddingLeft,
                contentLayout.paddingTop,
                contentLayout.paddingRight,
                buttonHeight + 16 // 额外增加一些空间
            )
        }
    }
    
    /**
     * 重置内容区域底部内边距
     */
    private fun resetContentBottomPadding(contentLayout: LinearLayout?) {
        if (contentLayout != null) {
            contentLayout.setPadding(
                contentLayout.paddingLeft,
                contentLayout.paddingTop,
                contentLayout.paddingRight,
                16 // 恢复默认内边距
            )
        }
    }
    
    // 新增方法：设置数据可视化
    private fun setupVisualization(view: View) {
        // 确保可视化容器存在
        ensureCombinedVisualizationContainer(view)

        // 在同一个容器中设置工单状态和处理时间
        setupCombinedStatusAndProcessingTime(view)
    }

    // 新增：创建合并后的容器
    private fun ensureCombinedVisualizationContainer(view: View) {
        // 查找主布局容器
        val nestedScrollView = view.findViewById<NestedScrollView>(R.id.nested_scroll_view)
        val mainContentLayout = nestedScrollView?.getChildAt(0) as? LinearLayout
        if (mainContentLayout == null) {
            Log.e("WorkOrderDetail", "无法找到主内容布局容器")
            return
        }

        // 检查可视化容器是否已存在
        if (view.findViewWithTag<LinearLayout>("combined_container") == null) {
            // 获取工单状态卡片的位置（通常是第一个卡片）
            val statusCardIndex = 0

            // 创建合并后的卡片
            val combinedCard = CardView(requireContext()).apply {
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
                ).apply {
                    leftMargin = resources.getDimensionPixelSize(R.dimen.default_padding)
                    rightMargin = resources.getDimensionPixelSize(R.dimen.default_padding)
                    topMargin = resources.getDimensionPixelSize(R.dimen.default_padding)
                    bottomMargin = 0
                }
                radius = resources.getDimension(R.dimen.card_corner_radius)
                cardElevation = resources.getDimension(R.dimen.card_elevation)
                setCardBackgroundColor(resources.getColor(R.color.white, null))
                preventCornerOverlap = true
                useCompatPadding = false
            }

            val combinedContainer = LinearLayout(requireContext()).apply {
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
                )
                orientation = LinearLayout.VERTICAL
                setPadding(
                    resources.getDimensionPixelSize(R.dimen.default_padding),
                    resources.getDimensionPixelSize(R.dimen.default_padding),
                    resources.getDimensionPixelSize(R.dimen.default_padding),
                    resources.getDimensionPixelSize(R.dimen.default_padding)
                )
                tag = "combined_container"
            }
            combinedCard.addView(combinedContainer)

            // 将合并后的卡片添加到工单状态卡片之后
            mainContentLayout.addView(combinedCard, statusCardIndex + 1)
        }
    }

    // 新增：实现合并后的内容展示
    private fun setupCombinedStatusAndProcessingTime(view: View) {
        // 查找合并后的容器
        val combinedContainer = view.findViewWithTag<LinearLayout>("combined_container") ?: return

        // 清除现有视图
        combinedContainer.removeAllViews()

        // 添加标题
        val titleView = TextView(requireContext()).apply {
            text = "工单状态与处理时间"
            textSize = 16f
            typeface = Typeface.DEFAULT_BOLD
            setTextColor(resources.getColor(R.color.text_primary, null))
            setPadding(0, 0, 0, 12)
        }
        combinedContainer.addView(titleView)

        // 如果工单未分配工程师，直接显示提示信息
        if (order.engineerId == null) {
            val noDataView = TextView(requireContext()).apply {
                text = "工单尚未分配工程师，无状态流转数据"
                setTextColor(resources.getColor(R.color.text_hint, null))
                textSize = 14f
                setPadding(0, 8, 0, 8)
                gravity = android.view.Gravity.CENTER
            }
            combinedContainer.addView(noDataView)
            return
        }

        // 加载工单详情数据
        val loadingView = TextView(requireContext()).apply {
            text = "正在加载工单状态数据..."
            setTextColor(resources.getColor(R.color.text_hint, null))
            textSize = 14f
            setPadding(0, 8, 0, 8)
            gravity = android.view.Gravity.CENTER
        }
        combinedContainer.addView(loadingView)

        // 调用API获取工单详情
        workOrderApi.getWorkDetail(order.id).enqueue(object : Callback<ApiResponse<Any>> {
            override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                if (!isAdded) return

                // 移除加载视图
                combinedContainer.removeAllViews()
                // 重新添加标题
                combinedContainer.addView(titleView)

                if (response.isSuccessful && response.body()?.code == 200 && response.body()?.data != null) {
                    try {
                        // 解析获取到的工单数据
                        val workOrderData = parseWorkOrderItem(response.body()?.data!!)
                        val workOrderDetail = mapToRepairOrder(workOrderData)

                        // 创建时间轴项目
                        val timelineItems = mutableListOf<Triple<Date?, String, Long?>>()

                        // 添加所有可能的状态点及其处理时间
                        timelineItems.add(Triple(workOrderDetail.createTime, "创建工单", null))

                        if (workOrderDetail.receiveTime != null) {
                            // 计算从创建到接单的时间
                            val duration = calculateDuration(workOrderDetail.createTime, workOrderDetail.receiveTime)
                            timelineItems.add(Triple(workOrderDetail.receiveTime, "工程师接单", duration))
                        }

                        if (workOrderDetail.departureTime != null) {
                            // 计算从接单到出发的时间
                            val duration = calculateDuration(workOrderDetail.receiveTime, workOrderDetail.departureTime)
                            timelineItems.add(Triple(workOrderDetail.departureTime, "工程师出发", duration))
                        }

                        if (workOrderDetail.actualArriveTime != null) {
                            // 计算从出发到到达的时间
                            val duration = calculateDuration(workOrderDetail.departureTime, workOrderDetail.actualArriveTime)
                            timelineItems.add(Triple(workOrderDetail.actualArriveTime, "工程师到达", duration))
                        }

                        if (workOrderDetail.sendReportTime != null) {
                            // 计算从到达到提交报告的时间
                            val duration = calculateDuration(workOrderDetail.actualArriveTime, workOrderDetail.sendReportTime)
                            timelineItems.add(Triple(workOrderDetail.sendReportTime, "提交报告", duration))
                        }

                        if (workOrderDetail.confirmReportTime != null) {
                            // 计算从提交报告到客户确认的时间
                            val duration = calculateDuration(workOrderDetail.sendReportTime, workOrderDetail.confirmReportTime)
                            timelineItems.add(Triple(workOrderDetail.confirmReportTime, "客户确认", duration))
                        }

                        if (workOrderDetail.completedAt != null) {
                            // 计算从客户确认到完成结算的时间
                            val duration = calculateDuration(workOrderDetail.confirmReportTime ?: workOrderDetail.sendReportTime, workOrderDetail.completedAt)
                            timelineItems.add(Triple(workOrderDetail.completedAt, "完成结算", duration))
                        }

                        // 过滤掉没有时间的状态项
                        val validTimelineItems = timelineItems.filter { it.first != null }

                        if (validTimelineItems.isEmpty()) {
                            // 如果没有有效的时间点数据
                            val noDataView = TextView(requireContext()).apply {
                                text = "暂无状态流转数据"
                                setTextColor(resources.getColor(R.color.text_hint, null))
                                textSize = 14f
                                setPadding(0, 8, 0, 8)
                                gravity = android.view.Gravity.CENTER
                            }
                            combinedContainer.addView(noDataView)
                            return
                        }

                        // 添加时间轴视图
                        for (index in validTimelineItems.indices) {
                            val item = validTimelineItems[index]

                            // 添加节点
                            addCombinedTimelineItem(
                                container = combinedContainer,
                                statusName = item.second,
                                statusTime = item.first!!,
                                index = index,
                                isLast = index == validTimelineItems.size - 1,
                                showDurationLine = index > 0 // 第一个节点不显示前置耗时线
                            )

                            // 添加耗时信息（在当前节点和下一节点之间）
                            if (index < validTimelineItems.size - 1) {
                                val nextItem = validTimelineItems[index + 1]
                                val duration = calculateDuration(item.first, nextItem.first)
                                if (duration != null) {
                                    // 根据当前节点和下一个节点的状态名称设置对应的耗时名称
                                    val durationName = when (item.second) {
                                        "创建工单" -> "接单耗时"
                                        "工程师接单" -> "准备耗时"
                                        "工程师出发" -> "路途耗时"
                                        "工程师到达" -> "维修耗时"
                                        "提交报告" -> "客户确认耗时"
                                        "客户确认" -> "客户结算耗时"
                                        else -> "处理耗时"
                                    }

                                    addDurationBetweenNodes(
                                        container = combinedContainer,
                                        duration = duration,
                                        durationName = durationName
                                    )
                                }
                            }
                        }

                        // 添加总处理时间
                        // 规则：已完成工单 = 完成时间 - 创建时间；未完成工单 = 当前时间 - 创建时间
                        val isCompleted = workOrderDetail.completedAt != null
                        val endTime = if (isCompleted) {
                            workOrderDetail.completedAt!!
                        } else {
                            Date() // 当前时间
                        }
                        addTotalProcessingTime(combinedContainer, workOrderDetail.createTime, endTime, isCompleted)

                    } catch (e: Exception) {
                        Log.e("WorkOrderDetail", "工单状态数据解析错误", e)
                        showCombinedLoadingError(combinedContainer)
                    }
                } else {
                    Log.e("WorkOrderDetail", "获取工单状态失败: ${response.code()}")
                    showCombinedLoadingError(combinedContainer)
                }
            }

            override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                if (!isAdded) return
                Log.e("WorkOrderDetail", "获取工单状态网络请求失败", t)
                showCombinedLoadingError(combinedContainer)
            }
        })
    }

    // 新增：添加合并后的时间轴项目
    private fun addCombinedTimelineItem(
        container: LinearLayout,
        statusName: String,
        statusTime: Date,
        index: Int,
        isLast: Boolean,
        showDurationLine: Boolean = false
    ) {
        // 创建一个时间轴项目视图
        val itemLayout = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.HORIZONTAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            setPadding(8, if (index == 0) 0 else 8, 8, 8)
        }

        // 创建包含指示点和线的垂直布局
        val verticalLayout = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            gravity = android.view.Gravity.CENTER_HORIZONTAL
        }

        // 添加状态指示点
        val indicatorView = View(requireContext()).apply {
            layoutParams = LinearLayout.LayoutParams(12, 12)
            background = requireContext().getDrawable(
                if (isLast) R.drawable.bg_timeline_indicator_completed
                else R.drawable.bg_timeline_indicator
            )
        }
        verticalLayout.addView(indicatorView)

        // 添加垂直线（除了最后一项）
        if (!isLast) {
            val lineView = View(requireContext()).apply {
                val params = LinearLayout.LayoutParams(2, 48)
                params.topMargin = 4
                layoutParams = params
                setBackgroundColor(resources.getColor(R.color.colorPrimary, null))
            }
            verticalLayout.addView(lineView)
        }

        itemLayout.addView(verticalLayout)

        // 添加状态信息布局
        val infoLayout = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            setPadding(16, 0, 0, 0)
        }

        // 创建名称和时间的水平布局
        val nameTimeLayout = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.HORIZONTAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
        }

        // 添加状态名称 (左侧)
        val statusNameView = TextView(requireContext()).apply {
            text = statusName
            textSize = 14f
            typeface = Typeface.DEFAULT_BOLD
            setTextColor(resources.getColor(R.color.text_primary, null))
            layoutParams = LinearLayout.LayoutParams(
                0,
                LinearLayout.LayoutParams.WRAP_CONTENT,
                1f // 权重为1，占据剩余空间
            )
        }
        nameTimeLayout.addView(statusNameView)

        // 添加状态时间 (右侧)
        val statusTimeView = TextView(requireContext()).apply {
            text = formatDateTimeIso(statusTime)
            textSize = 13f
            setTextColor(resources.getColor(R.color.text_secondary, null))
        }
        nameTimeLayout.addView(statusTimeView)

        // 将水平布局添加到信息布局中
        infoLayout.addView(nameTimeLayout)

        itemLayout.addView(infoLayout)
        container.addView(itemLayout)
    }

    // 新增：添加节点之间的耗时信息
    private fun addDurationBetweenNodes(
        container: LinearLayout,
        duration: Long,
        durationName: String = "耗时" // 添加耗时名称参数，默认为"耗时"
    ) {
        // 创建耗时显示布局
        val durationLayout = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.HORIZONTAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            setPadding(8, 0, 8, 0)
        }

        // 创建左侧垂直布局（用于对齐时间轴）
        val leftSpaceLayout = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            gravity = android.view.Gravity.CENTER_HORIZONTAL
        }

        // 添加空白占位，与时间轴对齐
        val spaceView = View(requireContext()).apply {
            layoutParams = LinearLayout.LayoutParams(12, 12)
            visibility = View.INVISIBLE
        }
        leftSpaceLayout.addView(spaceView)

        durationLayout.addView(leftSpaceLayout)

        // 添加耗时信息布局
        val durationInfoLayout = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            setPadding(16, 0, 0, 0)
            gravity = android.view.Gravity.CENTER_VERTICAL
        }

        // 添加耗时文本
        val durationView = TextView(requireContext()).apply {
            text = "$durationName: ${formatDuration(duration)}"
            textSize = 12f
            setTextColor(resources.getColor(R.color.colorPrimary, null))
            typeface = Typeface.DEFAULT_BOLD
            gravity = android.view.Gravity.CENTER_VERTICAL
        }
        durationInfoLayout.addView(durationView)

        durationLayout.addView(durationInfoLayout)
        container.addView(durationLayout)
    }

    // 新增：添加总处理时间
    private fun addTotalProcessingTime(container: LinearLayout, createTime: Date, endTime: Date, isCompleted: Boolean) {
        // 添加分割线
        val divider = View(requireContext()).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                resources.getDimensionPixelSize(R.dimen.divider_height)
            )
            setBackgroundColor(resources.getColor(R.color.stroke_light, null))
        }
        container.addView(divider)

        // 总处理时间布局
        val totalTimeLayout = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.HORIZONTAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            setPadding(16, 12, 16, 12)
            background = requireContext().getDrawable(R.drawable.bg_total_time)
        }

        // 计算时间差：endTime - createTime
        val duration = calculateDuration(createTime, endTime)

        // 根据工单状态确定标签和格式化结果
        val labelText = if (isCompleted) "总处理时间" else "已用时间"
        val formattedDuration = if (duration != null && duration >= 0) {
            formatDuration(duration)
        } else {
            Log.w("WorkOrderDetail", "时间计算异常: createTime=${formatDateTimeIso(createTime)}, endTime=${formatDateTimeIso(endTime)}, duration=$duration")
            "计算异常"
        }

        // 总时间标签
        val totalLabelView = TextView(requireContext()).apply {
            text = labelText
            setTextColor(resources.getColor(R.color.text_primary, null))
            typeface = Typeface.DEFAULT_BOLD
            textSize = 14f
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
        }
        totalTimeLayout.addView(totalLabelView)
        val totalDurationView = TextView(requireContext()).apply {
            text = formattedDuration
            setTextColor(resources.getColor(R.color.colorPrimary, null))
            typeface = Typeface.DEFAULT_BOLD
            textSize = 14f
        }
        totalTimeLayout.addView(totalDurationView)

        container.addView(totalTimeLayout)

        // 添加调试日志
        Log.d("WorkOrderDetail", "总处理时间计算 - 是否完成: $isCompleted, 标签: $labelText, 时间: $formattedDuration")
    }

    // 新增：计算时间差（结束时间 - 开始时间）
    private fun calculateDuration(startTime: Date?, endTime: Date?): Long? {
        if (startTime == null || endTime == null) {
            Log.w("WorkOrderDetail", "时间计算失败 - 开始时间或结束时间为null: startTime=$startTime, endTime=$endTime")
            return null
        }

        val duration = endTime.time - startTime.time

        // 添加详细的调试日志
        Log.d("WorkOrderDetail", "时间计算详情:")
        Log.d("WorkOrderDetail", "  开始时间: ${formatDateTimeIso(startTime)} (${startTime.time})")
        Log.d("WorkOrderDetail", "  结束时间: ${formatDateTimeIso(endTime)} (${endTime.time})")
        Log.d("WorkOrderDetail", "  时间差(毫秒): $duration")
        Log.d("WorkOrderDetail", "  时间差(秒): ${duration / 1000}")

        return duration
    }

    // 新增：格式化时间差
    private fun formatDuration(durationMillis: Long): String {
        // 处理负数情况
        if (durationMillis < 0) {
            Log.w("WorkOrderDetail", "检测到负数时间差: ${durationMillis}毫秒，可能存在时间计算错误")
            return "计算错误"
        }

        val seconds = durationMillis / 1000
        val minutes = seconds / 60
        val hours = minutes / 60
        val days = hours / 24

        return when {
            days > 0 -> "${days}天${hours % 24}小时${minutes % 60}分钟"
            hours > 0 -> "${hours}小时${minutes % 60}分钟"
            minutes > 0 -> "${minutes}分钟"
            else -> "${seconds}秒"
        }
    }

    // 新增：显示加载错误
    private fun showCombinedLoadingError(container: LinearLayout) {
        // 清除现有视图
        container.removeAllViews()

        // 添加标题
        val titleView = TextView(requireContext()).apply {
            text = "工单状态与处理时间"
            textSize = 16f
            typeface = Typeface.DEFAULT_BOLD
            setTextColor(resources.getColor(R.color.text_primary, null))
            setPadding(0, 0, 0, 12)
        }
        container.addView(titleView)

        // 添加错误提示
        val errorView = TextView(requireContext()).apply {
            text = "无法获取工单状态数据"
            setTextColor(resources.getColor(R.color.text_hint, null))
            textSize = 14f
            setPadding(0, 8, 0, 8)
            gravity = android.view.Gravity.CENTER
        }
        container.addView(errorView)
    }

    // 显示工单转派对话框
    private fun showReassignEngineerDialog(orderId: String) {
        // 直接使用成员变量 productId，如果为空则尝试从order对象获取
        var currentProductId = this.productId
        if (currentProductId.isNullOrBlank()) {
            // 尝试从当前加载的order对象中获取
            currentProductId = order.productId
            Log.d("WorkOrderDetail", "从order对象获取产品ID: ${currentProductId ?: "null"}")

            // 如果获取到了，更新成员变量
            if (!currentProductId.isNullOrBlank()) {
                this.productId = currentProductId
            }
        }

        if (currentProductId.isNullOrBlank()) {
            Toast.makeText(requireContext(), "工单缺少产品ID，无法转派工程师", Toast.LENGTH_SHORT).show()
            return
        }

        // 调用API获取工程师列表
        workOrderApi.getEngineerList(currentProductId).enqueue(object : Callback<EngineerListResponse> {
            override fun onResponse(call: Call<EngineerListResponse>, response: Response<EngineerListResponse>) {
                if (response.isSuccessful && response.body()?.code == 200) {
                    val engineers = response.body()?.getEngineerList() ?: emptyList()
                    if (engineers.isNotEmpty()) {
                        // 过滤掉当前工程师
                        val filteredEngineers = engineers.filter { it.id != order.engineerId }
                        if (filteredEngineers.isNotEmpty()) {
                            showEngineerSelectionForReassign(filteredEngineers)
                        } else {
                            Toast.makeText(requireContext(), "没有其他可用的工程师", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        Toast.makeText(requireContext(), "没有可用的工程师", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(requireContext(), "获取工程师列表失败", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<EngineerListResponse>, t: Throwable) {
                Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    // 显示工程师选择对话框(用于转派)
    private fun showEngineerSelectionForReassign(engineers: List<Engineer>) {
        val fullEngineers = engineers.toMutableList()
        val displayedEngineers = fullEngineers.toMutableList()
        val dialogView = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_engineer_selection, null)
        val recyclerView = dialogView.findViewById<RecyclerView>(R.id.recycler_engineers)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        val adapter = EngineerAdapter(displayedEngineers) { engineer ->
            confirmReassignEngineer(engineer)
        }
        recyclerView.adapter = adapter

        dialogView.findViewById<EditText?>(R.id.edit_search_engineer)?.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: android.text.Editable?) {
                val key = s?.toString()?.trim()?.lowercase() ?: ""
                val filtered = if (key.isEmpty()) fullEngineers else fullEngineers.filter {
                    it.name.lowercase().contains(key) || (it.phone?.contains(key) == true)
                }
                displayedEngineers.clear(); displayedEngineers.addAll(filtered)
                adapter.notifyDataSetChanged()
            }
        })

        lifecycleScope.launch {
            val dispatcher = Dispatchers.IO.limitedParallelism(5)
            fullEngineers.forEachIndexed { idx, eng ->
                launch(dispatcher) {
                    val phone = try {
                        val resp = workOrderApi.getEngineer(eng.id).execute()
                        if (resp.isSuccessful && resp.body()?.code == 200) {
                            (resp.body()?.data as? Map<*, *>)?.let { (it["userManageVo"] as? Map<*, *>)?.get("mobileNumber")?.toString() }
                        } else null
                    } catch (_: Exception) { null }
                    val workData = try {
                        val resp = workOrderApi.getEngineerWorkSummary(eng.id).execute()
                        if (resp.isSuccessful && resp.body()?.code == 200) resp.body()?.data as? EngineerWorkData else null
                    } catch (_: Exception) { null }
                    val updated = eng.copy(phone = phone)
                    withContext(Dispatchers.Main) {
                        fullEngineers[idx] = updated
                        val posDisp = displayedEngineers.indexOfFirst { it.id == updated.id }
                        if (posDisp != -1) {
                            displayedEngineers[posDisp] = updated
                            adapter.notifyItemChanged(posDisp)
                        }
                        if (workData != null) adapter.updateEngineerWorkData(updated.id, workData)
                    }
                }
            }
        }

        engineerSelectionDialog = AlertDialog.Builder(requireContext())
            .setTitle("选择转派工程师")
            .setView(dialogView)
            .setNegativeButton("取消", null)
            .show()
    }

    // 确认转派工程师
    private fun confirmReassignEngineer(engineer: Engineer) {
        AlertDialog.Builder(requireContext())
            .setTitle("确认转派")
            .setMessage("确定将该维修工单从${order.engineerName ?: "当前工程师"}转派给${engineer.name}吗？")
            .setPositiveButton("确定") { _, _ ->
                reassignEngineerToOrder(engineer)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    // 执行转派操作
    private fun reassignEngineerToOrder(engineer: Engineer) {
        val params = HashMap<String, String>()
        params["id"] = order.id
        params["engineerId"] = engineer.id
        
        // 使用标准的转派接口
        workOrderApi.transferWorkOrder(params).enqueue(object : Callback<ApiResponse<Any>> {
            override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                if (response.isSuccessful && response.body()?.code == 200) {
                    // 关闭工程师选择弹窗
                    engineerSelectionDialog?.dismiss()
                    engineerSelectionDialog = null
                    
                    Toast.makeText(requireContext(), "转派成功", Toast.LENGTH_SHORT).show()
                    
                    // 根据来源决定是否返回列表
                    if (isFromMyOrders) {
                        // 从我的工单进入的，返回列表并通知刷新
                        // 设置结果，通知列表页刷新数据
                        parentFragmentManager.setFragmentResult("refresh_order_list", Bundle().apply {
                            putBoolean("refresh", true)
                        })
                        
                        // 返回上一页
                        findNavController().navigateUp()
                    } else {
                        // 从其他页面进入的，只重新加载工单详情
                        loadWorkOrderDetail()
                    }
                } else {
                    Toast.makeText(requireContext(), "转派失败: ${response.body()?.msg ?: "未知错误"}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    // 显示分配工程师对话框
    private fun showAssignEngineerDialog(orderId: String) {
        // 直接使用成员变量 productId，如果为空则尝试从order对象获取
        var currentProductId = this.productId
        if (currentProductId.isNullOrBlank()) {
            // 尝试从当前加载的order对象中获取
            currentProductId = order.productId
            Log.d("WorkOrderDetail", "从order对象获取产品ID: ${currentProductId ?: "null"}")
            
            // 如果获取到了，更新成员变量
            if (!currentProductId.isNullOrBlank()) {
                this.productId = currentProductId
            }
        }
        
        if (currentProductId.isNullOrBlank()) {
            Toast.makeText(requireContext(), "工单缺少产品ID，无法分配工程师", Toast.LENGTH_SHORT).show()
            return
        }
        
        // 调用API获取可用工程师列表
        workOrderApi.getEngineerList(currentProductId).enqueue(object : Callback<EngineerListResponse> {
            override fun onResponse(call: Call<EngineerListResponse>, response: Response<EngineerListResponse>) {
                if (response.isSuccessful && response.body()?.code == 200) {
                    val engineers = response.body()?.getEngineerList() ?: emptyList()
                    if (engineers.isNotEmpty()) {
                        showEngineerSelectionDialog(engineers)
                    } else {
                        Toast.makeText(requireContext(), "没有可用的工程师", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(requireContext(), "获取工程师列表失败", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<EngineerListResponse>, t: Throwable) {
                Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    // 显示工程师选择对话框(用于分配)
    private fun showEngineerSelectionDialog(engineers: List<Engineer>) {
        val fullEngineers = engineers.toMutableList()
        val displayedEngineers = fullEngineers.toMutableList()
        val dialogView = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_engineer_selection, null)
        val recyclerView = dialogView.findViewById<RecyclerView>(R.id.recycler_engineers)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        val adapter = EngineerAdapter(displayedEngineers) { engineer ->
            confirmAssignEngineer(engineer)
        }
        recyclerView.adapter = adapter

        // 搜索功能
        dialogView.findViewById<EditText?>(R.id.edit_search_engineer)?.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: android.text.Editable?) {
                val key = s?.toString()?.trim()?.lowercase() ?: ""
                val filtered = if (key.isEmpty()) fullEngineers else fullEngineers.filter {
                    it.name.lowercase().contains(key) || (it.phone?.contains(key) == true)
                }
                displayedEngineers.clear(); displayedEngineers.addAll(filtered)
                adapter.notifyDataSetChanged()
            }
        })

        // 并发加载电话及工单概要并同步到两列表
        lifecycleScope.launch {
            val dispatcher = Dispatchers.IO.limitedParallelism(5)
            fullEngineers.forEachIndexed { idx, eng ->
                launch(dispatcher) {
                    val phone = try {
                        val resp = workOrderApi.getEngineer(eng.id).execute()
                        if (resp.isSuccessful && resp.body()?.code == 200) {
                            (resp.body()?.data as? Map<*, *>)?.let { (it["userManageVo"] as? Map<*, *>)?.get("mobileNumber")?.toString() }
                        } else null
                    } catch (_: Exception) { null }

                    val workData = try {
                        val resp = workOrderApi.getEngineerWorkSummary(eng.id).execute()
                        if (resp.isSuccessful && resp.body()?.code == 200) resp.body()?.data as? EngineerWorkData else null
                    } catch (_: Exception) { null }

                    val updated = eng.copy(phone = phone)
                    withContext(Dispatchers.Main) {
                        fullEngineers[idx] = updated
                        val posDisp = displayedEngineers.indexOfFirst { it.id == updated.id }
                        if (posDisp != -1) {
                            displayedEngineers[posDisp] = updated
                            adapter.notifyItemChanged(posDisp)
                        }
                        if (workData != null) adapter.updateEngineerWorkData(updated.id, workData)
                    }
                }
            }
        }

        AlertDialog.Builder(requireContext())
            .setTitle("选择工程师")
            .setView(dialogView)
            .setNegativeButton("取消", null)
            .show()
    }

    // 确认分配工程师
    private fun confirmAssignEngineer(engineer: Engineer) {
        AlertDialog.Builder(requireContext())
            .setTitle("确认分配")
            .setMessage("确定将该维修工单分配给${engineer.name}吗？")
            .setPositiveButton("确定") { _, _ ->
                assignEngineerToOrder(engineer)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    // 执行分配操作
    private fun assignEngineerToOrder(engineer: Engineer) {
        val params = HashMap<String, String>()
        params["id"] = order.id
        params["engineerId"] = engineer.id
        
        workOrderApi.confirmAssignWorkOrder(params).enqueue(object : Callback<ApiResponse<Any>> {
            override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                if (response.isSuccessful && response.body()?.code == 200) {
                    Toast.makeText(requireContext(), "分配成功", Toast.LENGTH_SHORT).show()
                    // 重新加载工单详情
                    loadWorkOrderDetail()
                } else {
                    Toast.makeText(requireContext(), "分配失败: ${response.body()?.msg ?: "未知错误"}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    
    // 新增：跳转到设备维修列表页面
    private fun navigateToDeviceMaintenanceList() {
        val groupId = deviceGroupId
        if (groupId.isNullOrEmpty()) {
            Toast.makeText(requireContext(), "缺少设备分组ID，无法查询历史记录", Toast.LENGTH_SHORT).show()
            return
        }
        
        // 创建Bundle传递参数
        val bundle = Bundle().apply {
            putString("deviceId", groupId)  // 设备维修列表Fragment期望的参数名称
            putString("customerId", order.customerId)
            putString("customerName", order.customerName)
            putString("recordType", "maintenance")
        }
        
        try {
            // 跳转到设备维修列表Fragment
            findNavController().navigate(R.id.deviceMaintenanceListFragment, bundle)
        } catch (e: Exception) {
            Log.e("WorkOrderDetail", "跳转到设备维修列表失败: ${e.message}", e)
            Toast.makeText(requireContext(), "跳转失败，请重试", Toast.LENGTH_SHORT).show()
        }
    }
    
    // 新增：历史工单弹窗
    private fun showHistoryWorkOrderDialog() {
        val context = requireContext()
        val dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_history_work_order, null)
        val recyclerView = dialogView.findViewById<RecyclerView>(R.id.recycler_history_work_order)
        recyclerView.layoutManager = LinearLayoutManager(context)
        val adapter = HistoryWorkOrderAdapter { workOrderId ->
            // 跳转到维修报告详情
            com.example.repairorderapp.ui.report.RepairReportDetailActivity.start(context, workOrderId)
        }
        recyclerView.adapter = adapter
        // 加载历史工单数据
        val groupId = deviceGroupId
        if (groupId.isNullOrEmpty()) {
            Toast.makeText(context, "缺少设备分组ID，无法查询历史记录", Toast.LENGTH_SHORT).show()
            return
        }
        workOrderApi.historyWorkOrderList(groupId).enqueue(object : Callback<ApiResponse<Any>> {
            override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                if (response.isSuccessful && response.body()?.code == 200) {
                    val data = response.body()?.data
                    // 解析为工单列表
                    val workOrderList = parseHistoryWorkOrderList(data)
                    adapter.submitList(workOrderList)
                } else {
                    Toast.makeText(context, "获取历史工单失败", Toast.LENGTH_SHORT).show()
                }
            }
            override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                Toast.makeText(context, "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
        AlertDialog.Builder(context)
            .setTitle("历史维修记录")
            .setView(dialogView)
            .setNegativeButton("关闭", null)
            .show()
    }
    // 解析历史工单列表
    private fun parseHistoryWorkOrderList(data: Any?): List<HistoryWorkOrder> {
        val result = mutableListOf<HistoryWorkOrder>()
        if (data is List<*>) {
            for (item in data) {
                if (item is Map<*, *>) {
                    val report = item["repairReport"] as? Map<*, *>
                    if (report != null) {
                        val id = report["workOrderId"]?.toString() ?: continue
                        val code = report["workOrderCode"]?.toString() ?: ""
                        val excDesc = report["excDesc"]?.toString() ?: ""
                        val createdAt = report["createdAt"]?.toString() ?: ""
                        result.add(HistoryWorkOrder(id, code, excDesc, createdAt))
                    }
                }
            }
        }
        return result.sortedByDescending { it.createdAt }
    }
    // 历史工单数据类
    data class HistoryWorkOrder(val id: String, val code: String, val excDesc: String, val createdAt: String)
    // 历史工单适配器
    private inner class HistoryWorkOrderAdapter(
        private val onReportClick: (String) -> Unit
    ) : RecyclerView.Adapter<HistoryWorkOrderAdapter.ViewHolder>() {
        private val data = mutableListOf<HistoryWorkOrder>()
        fun submitList(list: List<HistoryWorkOrder>) {
            data.clear()
            data.addAll(list)
            notifyDataSetChanged()
        }
        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val tvCode: TextView = itemView.findViewById(R.id.tv_history_order_code)
            val tvDesc: TextView = itemView.findViewById(R.id.tv_history_order_desc)
            val tvTime: TextView = itemView.findViewById(R.id.tv_history_order_time)
            val btnReport: Button = itemView.findViewById(R.id.btn_history_view_report)
        }
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(parent.context).inflate(R.layout.item_history_work_order, parent, false)
            return ViewHolder(view)
        }
        override fun getItemCount(): Int = data.size
        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val item = data[position]
            holder.tvCode.text = "工单号: ${item.code}"
            holder.tvDesc.text = item.excDesc
            holder.tvTime.text = item.createdAt
            holder.btnReport.setDebounceClickListener { onReportClick(item.id) }
        }
    }
    
    // 新增：零件更换记录弹窗
    private fun showReplaceRecordDialog() {
        val context = requireContext()
        val groupId = deviceGroupId
        if (groupId.isNullOrEmpty()) {
            Toast.makeText(context, "缺少设备分组ID，无法查询零件更换记录", Toast.LENGTH_SHORT).show()
            return
        }
        val params = mapOf(
            "pageNumber" to "1",
            "pageSize" to "100",
            "deviceGroupId" to groupId
        )
        workOrderApi.getReplaceRecordList(params).enqueue(object : retrofit2.Callback<com.example.repairorderapp.data.api.ApiResponse<Any>> {
            override fun onResponse(call: retrofit2.Call<com.example.repairorderapp.data.api.ApiResponse<Any>>, response: retrofit2.Response<com.example.repairorderapp.data.api.ApiResponse<Any>>) {
                if (response.isSuccessful && response.body()?.code == 200) {
                    val data = response.body()?.data
                    val recordList = mutableListOf<ReplaceRecord>()
                    if (data is Map<*, *>) {
                        val rows = data["rows"] as? List<*>
                        if (rows != null) {
                            for (item in rows) {
                                if (item is Map<*, *>) {
                                    val createdAt = item["createdAt"]?.toString() ?: ""
                                    val workCode = item["workCode"]?.toString() ?: ""
                                    val articleName = item["articleName"]?.toString() ?: ""
                                    val articleCode = item["articleCode"]?.toString() ?: ""
                                    val numberOem = item["numberOem"]?.toString() ?: ""
                                    val num = (item["num"] as? Number)?.toInt() ?: item["num"]?.toString()?.toIntOrNull() ?: 0
                                    val partId = item["partId"]?.toString() ?: ""
                                    val blackWhite = (item["blackWhite"] as? Number)?.toInt() ?: item["blackWhite"]?.toString()?.toIntOrNull() ?: 0
                                    val color = (item["color"] as? Number)?.toInt() ?: item["color"]?.toString()?.toIntOrNull() ?: 0
                                    recordList.add(ReplaceRecord(createdAt, workCode, articleName, articleCode, numberOem, num, partId, blackWhite, color))
                                }
                            }
                        }
                    }
                    // 按partId分组，组内按createdAt倒序
                    val grouped = recordList.groupBy { it.partId }
                    val sdf = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                    val allRecords = mutableListOf<ReplaceRecord>()
                    for ((_, list) in grouped) {
                        val sorted = list.sortedByDescending { it.createdAt }
                        for (i in sorted.indices) {
                            val cur = sorted[i]
                            if (i < sorted.size - 1) {
                                val prev = sorted[i + 1]
                                // 计算天数
                                try {
                                    val curDate = sdf.parse(cur.createdAt)
                                    val prevDate = sdf.parse(prev.createdAt)
                                    if (curDate != null && prevDate != null) {
                                        val diff = (curDate.time - prevDate.time) / (1000 * 60 * 60 * 24)
                                        cur.usageDays = diff.toInt()
                                    }
                                } catch (_: Exception) {}
                                // 计算印张数
                                cur.usagePages = (cur.blackWhite + cur.color) - (prev.blackWhite + prev.color)
                            }
                            allRecords.add(cur)
                        }
                    }
                    // 展示弹窗
                    val scrollView = android.widget.ScrollView(context)
                    val cardContainer = android.widget.LinearLayout(context).apply {
                        orientation = android.widget.LinearLayout.VERTICAL
                    }
                    scrollView.addView(cardContainer)
                    // 搜索框
                    val searchEdit = android.widget.EditText(context).apply {
                        hint = "搜索零件名称/编号/工单号"
                        setPadding(16, 16, 16, 16)
                        textSize = 14f
                        setBackgroundResource(android.R.drawable.edit_text)
                        val params = android.widget.LinearLayout.LayoutParams(
                            android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                            android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
                        )
                        params.setMargins(0, 0, 0, 24)
                        layoutParams = params
                    }
                    cardContainer.addView(searchEdit)
                    // 渲染卡片的函数
                    fun renderCards(list: List<ReplaceRecord>) {
                        cardContainer.removeAllViews()
                        for (rec in list) {
                            val card = android.widget.LinearLayout(context).apply {
                                orientation = android.widget.LinearLayout.VERTICAL
                                setPadding(32, 24, 32, 24)
                                val bg = android.graphics.drawable.GradientDrawable()
                                bg.setColor(0xFFFFFFFF.toInt())
                                bg.cornerRadius = 24f
                                bg.setStroke(2, 0xFFDDDDDD.toInt())
                                background = bg
                                val params = android.widget.LinearLayout.LayoutParams(
                                    android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                                    android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
                                )
                                params.setMargins(0, 0, 0, 32)
                                layoutParams = params
                                elevation = 6f
                            }
                            fun makeText(text: String, bold: Boolean = false, size: Float = 14f): android.widget.TextView {
                                return android.widget.TextView(context).apply {
                                    this.text = text
                                    setTextIsSelectable(true)
                                    setTextColor(0xFF222222.toInt())
                                    textSize = size
                                    if (bold) setTypeface(typeface, android.graphics.Typeface.BOLD)
                                    setPadding(0, 4, 0, 4)
                                }
                            }
                            card.addView(makeText("更换时间：${rec.createdAt}"))
                            card.addView(makeText("工单号：${rec.workCode}"))
                            card.addView(makeText("零件名称：${rec.articleName}"))
                            card.addView(makeText("零件编号：${rec.articleCode}"))
                            card.addView(makeText("OEM编号：${rec.numberOem}"))
                            card.addView(makeText("数量：${rec.num}"))
                            if (rec.usageDays != null) {
                                card.addView(makeText("更换使用天数：${rec.usageDays}", true))
                                card.addView(makeText("更换使用印张数：${rec.usagePages}", true))
                            } else {
                                card.addView(makeText("更换使用天数：首次更换", true))
                                card.addView(makeText("更换使用印张数：首次更换", true))
                            }
                            cardContainer.addView(card)
                        }
                    }
                    // 初始渲染全部
                    renderCards(allRecords)
                    // 搜索监听
                    searchEdit.addTextChangedListener(object : android.text.TextWatcher {
                        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                        override fun afterTextChanged(s: android.text.Editable?) {
                            val keyword = s?.toString()?.trim()?.lowercase() ?: ""
                            if (keyword.isEmpty()) {
                                renderCards(allRecords)
                            } else {
                                val filtered = allRecords.filter {
                                    it.articleName.lowercase().contains(keyword) ||
                                    it.articleCode.lowercase().contains(keyword) ||
                                    it.workCode.lowercase().contains(keyword) ||
                                    it.numberOem.lowercase().contains(keyword)
                                }
                                renderCards(filtered)
                            }
                        }
                    })
                    // 构建自定义弹窗根布局
                    val rootLayout = android.widget.LinearLayout(context).apply {
                        orientation = android.widget.LinearLayout.VERTICAL
                        setPadding(0, 0, 0, 0)
                    }
                    // 标题栏
                    val titleBar = android.widget.LinearLayout(context).apply {
                        orientation = android.widget.LinearLayout.HORIZONTAL
                        setPadding(32, 32, 32, 16)
                        val params = android.widget.LinearLayout.LayoutParams(
                            android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                            android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
                        )
                        layoutParams = params
                        gravity = android.view.Gravity.CENTER_VERTICAL
                    }
                    val titleText = android.widget.TextView(context).apply {
                        text = "零件更换记录"
                        textSize = 18f
                        setTypeface(typeface, android.graphics.Typeface.BOLD)
                        setTextColor(0xFF222222.toInt())
                        layoutParams = android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
                    }
                    val searchIcon = android.widget.ImageView(context).apply {
                        setImageResource(android.R.drawable.ic_menu_search)
                        setPadding(16, 0, 0, 0)
                        isClickable = true
                        isFocusable = true
                    }
                    titleBar.addView(titleText)
                    titleBar.addView(searchIcon)
                    rootLayout.addView(titleBar)
                    // 搜索框，初始隐藏
                    searchEdit.visibility = android.view.View.GONE
                    rootLayout.addView(searchEdit)
                    // 内容区（可滚动）
                    rootLayout.addView(scrollView)
                    // 搜索图标点击逻辑
                    searchIcon.setDebounceClickListener {
                        if (searchEdit.visibility == android.view.View.GONE) {
                            searchEdit.visibility = android.view.View.VISIBLE
                            searchEdit.requestFocus()
                            val imm = context.getSystemService(android.content.Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
                            imm.showSoftInput(searchEdit, android.view.inputmethod.InputMethodManager.SHOW_IMPLICIT)
                            searchIcon.setImageResource(android.R.drawable.ic_menu_close_clear_cancel)
                        } else {
                            searchEdit.visibility = android.view.View.GONE
                            searchEdit.setText("")
                            searchIcon.setImageResource(android.R.drawable.ic_menu_search)
                            val imm = context.getSystemService(android.content.Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
                            imm.hideSoftInputFromWindow(searchEdit.windowToken, 0)
                        }
                    }
                    searchEdit.setOnFocusChangeListener { _, hasFocus ->
                        if (!hasFocus && searchEdit.visibility == android.view.View.VISIBLE) {
                            searchEdit.visibility = android.view.View.GONE
                            searchEdit.setText("")
                            searchIcon.setImageResource(android.R.drawable.ic_menu_search)
                        }
                    }
                    android.app.AlertDialog.Builder(context)
                        .setView(rootLayout)
                        .setNegativeButton("关闭", null)
                        .show()
                } else {
                    Toast.makeText(context, "获取零件更换记录失败", Toast.LENGTH_SHORT).show()
                }
            }
            override fun onFailure(call: retrofit2.Call<com.example.repairorderapp.data.api.ApiResponse<Any>>, t: Throwable) {
                Toast.makeText(context, "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    // 新增：填写报告逻辑
    private fun fillRepairReport(orderId: String) {
        val intent = Intent(requireContext(), RepairReportEditActivity::class.java)
        intent.putExtra("orderId", orderId)
        intent.putExtra("customerId", order.customerId)
        intent.putExtra("deviceGroupId", order.deviceGroupId)
        reportEditLauncher.launch(intent)
    }
    
    /**
     * 显示撤回维修报告确认对话框
     */
    private fun showRecallReportConfirmDialog() {
        AlertDialog.Builder(requireContext())
            .setTitle("确认撤回")
            .setMessage("确定要撤回维修报告吗？")
            .setPositiveButton("确定") { _, _ ->
                recallReport()
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    /**
     * 撤回维修报告
     */
    private fun recallReport() {
        // 显示加载对话框
        val loadingDialog = ProgressDialog(requireContext())
        loadingDialog.setMessage("正在撤回报告...")
        loadingDialog.setCancelable(false)
        loadingDialog.show()
        
        // 调用撤回报告API
        workOrderApi.recallWorkOrder(order.id).enqueue(object : Callback<ApiResponse<Any>> {
            override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                loadingDialog.dismiss()
                
                if (response.isSuccessful && response.body()?.code == 200) {
                    Toast.makeText(requireContext(), "维修报告已撤回", Toast.LENGTH_SHORT).show()
                    // 重新加载工单详情
                    loadWorkOrderDetail()
                } else {
                    val errorMsg = response.body()?.msg ?: "未知错误"
                    Toast.makeText(requireContext(), "撤回失败: $errorMsg", Toast.LENGTH_SHORT).show()
                    Log.e("WorkOrderDetail", "撤回维修报告失败: $errorMsg")
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                loadingDialog.dismiss()
                Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
                Log.e("WorkOrderDetail", "撤回维修报告网络错误", t)
            }
        })
    }

    /**
     * 加载客户评价信息
     */
    @kotlinx.coroutines.ExperimentalCoroutinesApi
    private fun loadCustomerEvaluation(view: View) {
        lifecycleScope.launch {
            try {
                val response = workOrderApi.getWorkOrderEvaluationDetail(orderId!!).await()
                
                if (response.isSuccessful && response.body() != null) {
                    val apiResponse = response.body()!!
                    if (apiResponse.code == 200 && apiResponse.data != null) {
                        val workEvaluate = apiResponse.data.workEvaluate
                        if (workEvaluate != null) {
                            showCustomerEvaluation(view, workEvaluate)
                        } else {
                            hideCustomerEvaluation(view)
                        }
                    } else {
                        hideCustomerEvaluation(view)
                    }
                } else {
                    hideCustomerEvaluation(view)
                }
            } catch (e: Exception) {
                Log.e("WorkOrderDetail", "加载客户评价失败", e)
                hideCustomerEvaluation(view)
            }
        }
    }
    
    /**
     * 显示客户评价信息
     */
    private fun showCustomerEvaluation(view: View, evaluation: WorkEvaluate) {
        val cardEvaluation = view.findViewById<CardView>(R.id.card_customer_evaluation)
        cardEvaluation.visibility = View.VISIBLE
        
        // 设置专业能力评分
        setStarRating(view, "professional", evaluation.professional)
        view.findViewById<TextView>(R.id.tv_professional_score).text = "${evaluation.professional}分"
        
        // 设置服务态度评分
        setStarRating(view, "service", evaluation.service)
        view.findViewById<TextView>(R.id.tv_service_score).text = "${evaluation.service}分"
        
        // 设置评价时间
        view.findViewById<TextView>(R.id.tv_evaluation_time).text = evaluation.createdAt ?: "未知时间"
        
        // 设置评价内容
        val contentTextView = view.findViewById<TextView>(R.id.tv_evaluation_content)
        if (evaluation.content.isNullOrBlank()) {
            contentTextView.text = "客户未填写评价内容"
            contentTextView.setTextColor(requireContext().getColor(R.color.text_hint))
        } else {
            contentTextView.text = evaluation.content
            contentTextView.setTextColor(requireContext().getColor(R.color.text_content))
        }
    }
    
    /**
     * 设置星级评分显示
     */
    private fun setStarRating(view: View, type: String, rating: Int) {
        val starIds = when (type) {
            "professional" -> listOf(
                R.id.iv_professional_star_1,
                R.id.iv_professional_star_2,
                R.id.iv_professional_star_3,
                R.id.iv_professional_star_4,
                R.id.iv_professional_star_5
            )
            "service" -> listOf(
                R.id.iv_service_star_1,
                R.id.iv_service_star_2,
                R.id.iv_service_star_3,
                R.id.iv_service_star_4,
                R.id.iv_service_star_5
            )
            else -> return
        }
        
        starIds.forEachIndexed { index, starId ->
            val starImageView = view.findViewById<ImageView>(starId)
            if (index < rating) {
                starImageView.setImageResource(R.drawable.ic_star_filled)
            } else {
                starImageView.setImageResource(R.drawable.ic_star_empty)
            }
        }
    }
    
    /**
     * 隐藏客户评价卡片
     */
    private fun hideCustomerEvaluation(view: View) {
        val cardEvaluation = view.findViewById<CardView>(R.id.card_customer_evaluation)
        cardEvaluation.visibility = View.GONE
    }

    override fun onDestroyView() {
        super.onDestroyView()
        
        // 关闭工程师选择弹窗，避免内存泄漏
        engineerSelectionDialog?.dismiss()
        engineerSelectionDialog = null
    }

    /**
     * 设置客户信息卡片点击事件
     */
    private fun setupCustomerInfoClickListener(view: View) {
        // 通过遍历CardView找到包含客户信息的基本信息卡片
        val nestedScrollView = view.findViewById<NestedScrollView>(R.id.nested_scroll_view)
        val mainContentLayout = nestedScrollView?.getChildAt(0) as? LinearLayout

        if (mainContentLayout != null) {
            // 基本信息卡片是第二个CardView（第一个是状态卡片）
            for (i in 0 until mainContentLayout.childCount) {
                val child = mainContentLayout.getChildAt(i)
                if (child is CardView) {
                    // 检查CardView内部是否包含客户名称TextView
                    val customerNameView = child.findViewById<TextView>(R.id.tv_detail_customer_name)
                    if (customerNameView != null) {
                        // 找到了包含客户信息的卡片，给整个卡片添加点击事件
                        child.setDebounceClickListener {
                            navigateToCustomerDetail()
                        }
                        // 给卡片添加点击效果
                        val typedValue = android.util.TypedValue()
                        requireContext().theme.resolveAttribute(android.R.attr.selectableItemBackground, typedValue, true)
                        child.foreground = requireContext().getDrawable(typedValue.resourceId)
                        child.isClickable = true
                        child.isFocusable = true
                        return
                    }
                }
            }
        }

        // 如果找不到卡片，则给客户名称TextView添加点击事件作为备选
        view.findViewById<TextView>(R.id.tv_detail_customer_name)?.setDebounceClickListener {
            navigateToCustomerDetail()
        }
    }

    /**
     * 设置设备信息卡片点击事件
     */
    private fun setupDeviceInfoClickListener(view: View) {
        // 通过遍历CardView找到包含设备信息的设备信息卡片
        val nestedScrollView = view.findViewById<NestedScrollView>(R.id.nested_scroll_view)
        val mainContentLayout = nestedScrollView?.getChildAt(0) as? LinearLayout

        if (mainContentLayout != null) {
            // 查找包含设备信息的CardView
            for (i in 0 until mainContentLayout.childCount) {
                val child = mainContentLayout.getChildAt(i)
                if (child is CardView) {
                    // 检查CardView内部是否包含设备组TextView
                    val deviceGroupView = child.findViewById<TextView>(R.id.tv_detail_device_group)
                    if (deviceGroupView != null) {
                        // 找到了包含设备信息的卡片，给整个卡片添加点击事件
                        child.setDebounceClickListener {
                            navigateToDeviceDetail()
                        }
                        // 给卡片添加点击效果
                        val typedValue = android.util.TypedValue()
                        requireContext().theme.resolveAttribute(android.R.attr.selectableItemBackground, typedValue, true)
                        child.foreground = requireContext().getDrawable(typedValue.resourceId)
                        child.isClickable = true
                        child.isFocusable = true
                        return
                    }
                }
            }
        }

        // 如果找不到卡片，则给设备型号TextView添加点击事件作为备选
        view.findViewById<TextView>(R.id.tv_detail_machine_model)?.setDebounceClickListener {
            navigateToDeviceDetail()
        }
    }

    /**
     * 跳转到客户详情页面
     */
    private fun navigateToCustomerDetail() {
        if (order.customerId.isNullOrEmpty()) {
            Toast.makeText(requireContext(), "客户ID不能为空", Toast.LENGTH_SHORT).show()
            return
        }

        try {
            val bundle = Bundle().apply {
                putString("customerId", order.customerId)
                // 可以传递客户图片URL，如果有的话
                if (!order.deviceGroupImg.isNullOrEmpty()) {
                    putString("customerImageUrl", order.deviceGroupImg)
                }
            }

            // 使用导航组件跳转到客户详情页面
            findNavController().navigate(R.id.customerDetailFragment, bundle)
        } catch (e: Exception) {
            Log.e("WorkOrderDetail", "跳转到客户详情失败: ${e.message}", e)
            Toast.makeText(requireContext(), "跳转失败，请重试", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 跳转到设备详情页面
     */
    private fun navigateToDeviceDetail() {
        // 使用deviceGroupId作为设备ID
        val deviceId = deviceGroupId ?: order.deviceGroupId
        if (deviceId.isNullOrEmpty()) {
            Toast.makeText(requireContext(), "设备ID不能为空", Toast.LENGTH_SHORT).show()
            return
        }

        try {
            val bundle = Bundle().apply {
                putString("deviceId", deviceId)
                putString("customerId", order.customerId)
                putString("customerName", order.customerName)
            }

            // 使用导航组件跳转到设备详情页面
            findNavController().navigate(R.id.customerDeviceDetailFragment, bundle)
        } catch (e: Exception) {
            Log.e("WorkOrderDetail", "跳转到设备详情失败: ${e.message}", e)
            Toast.makeText(requireContext(), "跳转失败，请重试", Toast.LENGTH_SHORT).show()
        }
    }
}