package com.example.repairorderapp.ui.statistics

import android.os.Bundle

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.repairorderapp.R
import com.example.repairorderapp.databinding.FragmentStatisticsBinding
import com.example.repairorderapp.model.statistics.MonthTimelinessVO
import com.example.repairorderapp.ui.statistics.adapter.StatisticsAdapter
import com.example.repairorderapp.ui.warehouse.PaginationScrollListener
import com.example.repairorderapp.viewmodel.StatisticsViewModel
import com.google.android.material.snackbar.Snackbar

/**
 * 时效统计主页面Fragment
 * 显示工程师月度时效统计列表
 */
class StatisticsFragment : Fragment() {
    
    companion object {
        private const val TAG = "StatisticsFragment"
    }
    
    private var _binding: FragmentStatisticsBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var viewModel: StatisticsViewModel
    private lateinit var adapter: StatisticsAdapter
    

    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentStatisticsBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        Log.d(TAG, "StatisticsFragment onViewCreated")
        
        setupViewModel()
        setupRecyclerView()
        setupSwipeRefresh()
        setupFilterButton()
        observeData()
        
        // 初始加载数据
        viewModel.loadStatistics(refresh = true)
    }
    
    private fun setupViewModel() {
        viewModel = ViewModelProvider(this)[StatisticsViewModel::class.java]
    }
    
    private fun setupRecyclerView() {
        adapter = StatisticsAdapter { statisticsItem ->
            // 点击跳转到明细页面
            navigateToDetail(
                statisticsItem.engineerId, 
                statisticsItem.monthly, 
                statisticsItem.engineerName
            )
        }
        
        binding.recyclerView.adapter = adapter
        binding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
        
        // 添加分页加载监听
        binding.recyclerView.addOnScrollListener(object : PaginationScrollListener(
            binding.recyclerView.layoutManager as LinearLayoutManager
        ) {
            override fun loadMoreItems() {
                viewModel.loadMore()
            }
            
            override fun isLastPage(): Boolean = viewModel.isLastPagePublic
            override fun isLoading(): Boolean = viewModel.isLoadingMorePublic
        })
    }
    

    
    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.loadStatistics(refresh = true)
        }
    }
    
    private fun setupFilterButton() {
        binding.btnFilter.setOnClickListener {
            showFilterDialog()
        }
    }
    
    private fun observeData() {
        // 观察统计数据
        viewModel.statisticsList.observe(viewLifecycleOwner) { list ->
            Log.d(TAG, "统计数据更新: ${list.size}条")
            val previousSize = adapter.currentList.size

            // 更新数据总数显示
            updateDataCount(list.size)

            adapter.submitList(list) {
                // 数据更新完成后，在以下情况滚动到顶部：
                // 1. 刷新操作
                // 2. 数据从空变为有数据（首次加载）
                // 3. 数据大小没有增加（排序或筛选操作）
                val shouldScrollToTop = viewModel.isRefreshing.value == true ||
                                      (previousSize == 0 && list.isNotEmpty()) ||
                                      (previousSize > 0 && list.size <= previousSize)

                if (shouldScrollToTop) {
                    binding.recyclerView.scrollToPosition(0)
                    Log.d(TAG, "滚动到顶部")
                }
            }
        }
        
        // 观察加载状态
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.isVisible = isLoading && !binding.swipeRefreshLayout.isRefreshing
        }
        
        // 观察刷新状态
        viewModel.isRefreshing.observe(viewLifecycleOwner) { isRefreshing ->
            binding.swipeRefreshLayout.isRefreshing = isRefreshing
        }
        
        // 观察错误消息
        viewModel.errorMessage.observe(viewLifecycleOwner) { message ->
            if (message.isNotEmpty()) {
                Log.e(TAG, "显示错误消息: $message")
                showError(message)
            }
        }
        
        // 观察空状态
        viewModel.showEmpty.observe(viewLifecycleOwner) { showEmpty ->
            updateEmptyState(showEmpty)
        }
    }
    
    private fun updateEmptyState(isEmpty: Boolean) {
        binding.layoutEmpty.isVisible = isEmpty && !binding.progressBar.isVisible
        binding.recyclerView.isVisible = !isEmpty
        binding.layoutError.isVisible = false
    }
    
    private fun showError(message: String) {
        // 显示错误状态
        binding.layoutError.isVisible = true
        binding.layoutEmpty.isVisible = false
        binding.recyclerView.isVisible = false
        
        // 设置重试按钮
        binding.btnRetry.setOnClickListener {
            viewModel.retry()
        }
        
        // 同时显示Snackbar
        Snackbar.make(binding.root, message, Snackbar.LENGTH_LONG)
            .setAction("重试") {
                viewModel.retry()
            }
            .show()
    }
    
    private fun showFilterDialog() {
        val dialog = StatisticsFilterDialog()
        dialog.setCurrentFilter(viewModel.getCurrentFilterState())
        dialog.setOnFilterAppliedListener { filterState ->
            Log.d(TAG, "应用筛选条件: $filterState")
            viewModel.applyFilter(filterState)
        }
        dialog.show(parentFragmentManager, "filter_dialog")
    }
    
    private fun navigateToDetail(engineerId: Long, monthly: String, engineerName: String) {
        Log.d(TAG, "导航到明细页面: engineerId=$engineerId, monthly=$monthly, engineerName=$engineerName")

        try {
            // 使用Bundle传递参数
            val bundle = Bundle().apply {
                putLong("engineerId", engineerId)
                putString("monthly", monthly)
                putString("engineerName", engineerName)
            }
            findNavController().navigate(R.id.statisticsDetailFragment, bundle)
        } catch (e: Exception) {
            Log.e(TAG, "导航失败", e)
            Snackbar.make(binding.root, "页面跳转失败", Snackbar.LENGTH_SHORT).show()
        }
    }

    /**
     * 更新数据总数显示
     */
    private fun updateDataCount(count: Int) {
        if (count > 0) {
            binding.tvDataCount.text = "共 $count 条数据"
            binding.tvDataCount.visibility = View.VISIBLE
        } else {
            binding.tvDataCount.visibility = View.GONE
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        Log.d(TAG, "StatisticsFragment onDestroyView")

        _binding = null
    }
}
