package com.example.repairorderapp.base

import android.app.Dialog
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.example.repairorderapp.RepairOrderApp

/**
 * 基础Activity类
 * 提供全局触摸事件处理，支持自动隐藏软键盘功能
 */
open class BaseActivity : AppCompatActivity() {

    private val activeDialogs = mutableSetOf<Dialog>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 基类初始化，子类可以重写并调用super
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        // 处理全局触摸事件
        ev?.let { event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                handleGlobalTouch(event)
            }
        }

        // 继续正常的事件分发
        return super.dispatchTouchEvent(ev)
    }

    /**
     * 处理全局触摸事件
     * 包括Activity主界面和弹窗
     */
    private fun handleGlobalTouch(event: MotionEvent) {
        var touchHandled = false

        // 首先检查是否有活动的弹窗
        for (dialog in activeDialogs.toList()) {
            if (dialog.isShowing) {
                if (handleDialogTouch(dialog, event)) {
                    touchHandled = true
                    break
                }
            } else {
                // 移除已关闭的弹窗
                activeDialogs.remove(dialog)
            }
        }

        // 如果弹窗没有处理触摸事件，则处理Activity主界面
        if (!touchHandled) {
            RepairOrderApp.handleActivityTouch(this, event)
        }
    }

    /**
     * 处理弹窗中的触摸事件
     */
    private fun handleDialogTouch(dialog: Dialog, event: MotionEvent): Boolean {
        try {
            val dialogWindow = dialog.window ?: return false
            val decorView = dialogWindow.decorView as? ViewGroup ?: return false

            // 检查触摸点是否在弹窗内
            val location = IntArray(2)
            decorView.getLocationOnScreen(location)
            val dialogX = location[0]
            val dialogY = location[1]
            val dialogWidth = decorView.width
            val dialogHeight = decorView.height

            val isInsideDialog = event.rawX >= dialogX &&
                    event.rawX <= dialogX + dialogWidth &&
                    event.rawY >= dialogY &&
                    event.rawY <= dialogY + dialogHeight

            if (isInsideDialog) {
                // 触摸在弹窗内，检查是否触摸了EditText
                val touchedEditText = checkEditTextTouch(decorView, event)
                if (!touchedEditText) {
                    // 没有触摸EditText，隐藏键盘
                    RepairOrderApp.hideKeyboard(this)
                }
                return true // 弹窗处理了事件
            }
        } catch (e: Exception) {
            android.util.Log.e("BaseActivity", "处理弹窗触摸事件失败: ${e.message}")
        }

        return false // 弹窗没有处理事件
    }

    /**
     * 递归检查ViewGroup中是否有EditText被触摸
     */
    private fun checkEditTextTouch(viewGroup: ViewGroup, event: MotionEvent): Boolean {
        for (i in 0 until viewGroup.childCount) {
            val child = viewGroup.getChildAt(i)

            when {
                child is EditText && isTouchInsideEditText(child, event) -> {
                    return true
                }
                child is ViewGroup -> {
                    if (checkEditTextTouch(child, event)) {
                        return true
                    }
                }
            }
        }
        return false
    }

    /**
     * 检查触摸点是否在EditText范围内
     */
    private fun isTouchInsideEditText(view: View, event: MotionEvent): Boolean {
        if (view !is EditText) return false

        val location = IntArray(2)
        view.getLocationOnScreen(location)
        val x = location[0]
        val y = location[1]
        val width = view.width
        val height = view.height

        return event.rawX >= x && event.rawX <= x + width &&
                event.rawY >= y && event.rawY <= y + height
    }

    /**
     * 显示AlertDialog，并自动注册到活动弹窗列表
     */
    protected fun showDialog(dialog: AlertDialog) {
        try {
            activeDialogs.add(dialog)
            dialog.setOnDismissListener {
                activeDialogs.remove(dialog)
            }
            dialog.show()
            android.util.Log.d("BaseActivity", "已显示弹窗，当前活动弹窗数量: ${activeDialogs.size}")
        } catch (e: Exception) {
            android.util.Log.e("BaseActivity", "显示弹窗失败: ${e.message}")
            activeDialogs.remove(dialog)
        }
    }

    /**
     * 显示自定义Dialog，并自动注册到活动弹窗列表
     */
    protected fun showDialog(dialog: Dialog) {
        try {
            activeDialogs.add(dialog)
            dialog.setOnDismissListener {
                activeDialogs.remove(dialog)
            }
            dialog.show()
            android.util.Log.d("BaseActivity", "已显示自定义弹窗，当前活动弹窗数量: ${activeDialogs.size}")
        } catch (e: Exception) {
            android.util.Log.e("BaseActivity", "显示自定义弹窗失败: ${e.message}")
            activeDialogs.remove(dialog)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清理所有弹窗引用
        try {
            for (dialog in activeDialogs.toList()) {
                if (dialog.isShowing) {
                    dialog.dismiss()
                }
            }
            activeDialogs.clear()
        } catch (e: Exception) {
            android.util.Log.e("BaseActivity", "清理弹窗失败: ${e.message}")
        }
    }
}