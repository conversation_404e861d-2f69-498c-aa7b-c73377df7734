package com.example.repairorderapp.base

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import com.example.repairorderapp.utils.KeyboardUtils

/**
 * 基础Fragment类
 * 提供自动隐藏软键盘功能
 */
open class BaseFragment : Fragment() {

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 为Fragment的根View设置自动隐藏键盘功能
        setupAutoHideKeyboard(view)
    }

    /**
     * 设置自动隐藏键盘功能
     */
    private fun setupAutoHideKeyboard(rootView: View) {
        try {
            KeyboardUtils.setupAutoHideKeyboardForFragment(this, rootView)
            android.util.Log.d("BaseFragment", "已为 ${this::class.java.simpleName} 设置自动隐藏键盘功能")
        } catch (e: Exception) {
            android.util.Log.e("BaseFragment", "设置自动隐藏键盘功能失败: ${e.message}")
        }
    }

    /**
     * 手动隐藏键盘
     */
    protected fun hideKeyboard() {
        try {
            activity?.let { act ->
                view?.let { v ->
                    KeyboardUtils.hideKeyboard(act, v)
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("BaseFragment", "手动隐藏键盘失败: ${e.message}")
        }
    }

    /**
     * 检查键盘是否显示
     */
    protected fun isKeyboardVisible(): Boolean {
        return try {
            activity?.let { act ->
                KeyboardUtils.isKeyboardVisible(act)
            } ?: false
        } catch (e: Exception) {
            android.util.Log.e("BaseFragment", "检查键盘状态失败: ${e.message}")
            false
        }
    }
}